{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 婴幼儿语言发展指导数据集分析\n", "\n", "本notebook用于分析婴幼儿语言发展指导数据集的基本统计信息和质量指标。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import numpy as np\n", "from collections import Counter\n", "import jieba\n", "from wordcloud import WordCloud\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# 设置图表样式\n", "sns.set_style(\"whitegrid\")\n", "plt.style.use('seaborn-v0_8')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 数据加载"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_dataset(file_path):\n", "    \"\"\"加载数据集\"\"\"\n", "    if not Path(file_path).exists():\n", "        print(f\"数据文件不存在: {file_path}\")\n", "        return None\n", "    \n", "    with open(file_path, 'r', encoding='utf-8') as f:\n", "        data = json.load(f)\n", "    \n", "    return pd.DataFrame(data)\n", "\n", "# 加载最终数据集\n", "df = load_dataset('../data/final/infant_language_development_dataset.json')\n", "\n", "if df is not None:\n", "    print(f\"数据集大小: {df.shape}\")\n", "    print(f\"列名: {list(df.columns)}\")\n", "    df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 基本统计信息"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df is not None:\n", "    print(\"=== 数据集基本信息 ===\")\n", "    print(f\"总记录数: {len(df)}\")\n", "    print(f\"字段数: {len(df.columns)}\")\n", "    print(f\"缺失值统计:\")\n", "    print(df.isnull().sum())\n", "    \n", "    print(\"\\n=== 内容长度统计 ===\")\n", "    df['content_length'] = df['content'].str.len()\n", "    print(f\"平均长度: {df['content_length'].mean():.2f}\")\n", "    print(f\"最短长度: {df['content_length'].min()}\")\n", "    print(f\"最长长度: {df['content_length'].max()}\")\n", "    print(f\"中位数长度: {df['content_length'].median():.2f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 数据分布分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df is not None:\n", "    # 创建子图\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "    \n", "    # 按数据来源分布\n", "    source_counts = df['source'].value_counts()\n", "    axes[0, 0].pie(source_counts.values, labels=source_counts.index, autopct='%1.1f%%')\n", "    axes[0, 0].set_title('数据来源分布')\n", "    \n", "    # 按年龄段分布\n", "    age_counts = df['age_range'].value_counts()\n", "    axes[0, 1].bar(age_counts.index, age_counts.values)\n", "    axes[0, 1].set_title('年龄段分布')\n", "    axes[0, 1].tick_params(axis='x', rotation=45)\n", "    \n", "    # 按内容类型分布\n", "    content_type_counts = df['content_type'].value_counts()\n", "    axes[1, 0].bar(content_type_counts.index, content_type_counts.values)\n", "    axes[1, 0].set_title('内容类型分布')\n", "    axes[1, 0].tick_params(axis='x', rotation=45)\n", "    \n", "    # 内容长度分布\n", "    axes[1, 1].hist(df['content_length'], bins=30, alpha=0.7)\n", "    axes[1, 1].set_title('内容长度分布')\n", "    axes[1, 1].set_xlabel('字符数')\n", "    axes[1, 1].set_ylabel('频次')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 关键词分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df is not None:\n", "    # 提取所有关键词\n", "    all_keywords = []\n", "    for _, row in df.iterrows():\n", "        metadata = row.get('metadata', {})\n", "        if isinstance(metadata, dict):\n", "            keywords = metadata.get('keywords', [])\n", "            if isinstance(keywords, list):\n", "                all_keywords.extend(keywords)\n", "    \n", "    # 统计关键词频次\n", "    keyword_counts = Counter(all_keywords)\n", "    top_keywords = keyword_counts.most_common(20)\n", "    \n", "    print(\"=== 高频关键词 ===\")\n", "    for keyword, count in top_keywords:\n", "        print(f\"{keyword}: {count}\")\n", "    \n", "    # 绘制关键词频次图\n", "    if top_keywords:\n", "        keywords, counts = zip(*top_keywords)\n", "        plt.figure(figsize=(12, 6))\n", "        plt.bar(keywords, counts)\n", "        plt.title('高频关键词分布')\n", "        plt.xlabel('关键词')\n", "        plt.ylabel('频次')\n", "        plt.xticks(rotation=45)\n", "        plt.tight_layout()\n", "        plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 文本内容分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df is not None:\n", "    # 合并所有文本内容\n", "    all_text = ' '.join(df['content'].astype(str))\n", "    \n", "    # 中文分词\n", "    words = jieba.lcut(all_text)\n", "    \n", "    # 过滤停用词和短词\n", "    stop_words = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'}\n", "    filtered_words = [word for word in words if len(word) > 1 and word not in stop_words]\n", "    \n", "    # 统计词频\n", "    word_counts = Counter(filtered_words)\n", "    top_words = word_counts.most_common(30)\n", "    \n", "    print(\"=== 高频词汇 ===\")\n", "    for word, count in top_words[:15]:\n", "        print(f\"{word}: {count}\")\n", "    \n", "    # 绘制词频图\n", "    if top_words:\n", "        words, counts = zip(*top_words[:20])\n", "        plt.figure(figsize=(12, 6))\n", "        plt.bar(words, counts)\n", "        plt.title('高频词汇分布')\n", "        plt.xlabel('词汇')\n", "        plt.ylabel('频次')\n", "        plt.xticks(rotation=45)\n", "        plt.tight_layout()\n", "        plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 年龄段内容分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df is not None:\n", "    # 按年龄段分析内容长度\n", "    age_content_stats = df.groupby('age_range')['content_length'].agg(['count', 'mean', 'std', 'min', 'max'])\n", "    print(\"=== 各年龄段内容统计 ===\")\n", "    print(age_content_stats)\n", "    \n", "    # 绘制箱线图\n", "    plt.figure(figsize=(12, 6))\n", "    df.boxplot(column='content_length', by='age_range', ax=plt.gca())\n", "    plt.title('各年龄段内容长度分布')\n", "    plt.xlabel('年龄段')\n", "    plt.ylabel('内容长度')\n", "    plt.xticks(rotation=45)\n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 数据质量评估"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df is not None:\n", "    print(\"=== 数据质量评估 ===\")\n", "    \n", "    # 检查必填字段完整性\n", "    required_fields = ['id', 'source', 'category', 'age_range', 'content']\n", "    for field in required_fields:\n", "        missing_count = df[field].isnull().sum()\n", "        missing_rate = missing_count / len(df) * 100\n", "        print(f\"{field} 缺失率: {missing_rate:.2f}% ({missing_count}/{len(df)})\")\n", "    \n", "    # 检查内容质量\n", "    empty_content = df['content'].str.strip().eq('').sum()\n", "    short_content = (df['content_length'] < 50).sum()\n", "    long_content = (df['content_length'] > 5000).sum()\n", "    \n", "    print(f\"\\n空内容数量: {empty_content}\")\n", "    print(f\"过短内容数量 (<50字符): {short_content}\")\n", "    print(f\"过长内容数量 (>5000字符): {long_content}\")\n", "    \n", "    # 检查ID唯一性\n", "    duplicate_ids = df['id'].duplicated().sum()\n", "    print(f\"重复ID数量: {duplicate_ids}\")\n", "    \n", "    # 计算质量得分\n", "    total_records = len(df)\n", "    quality_issues = empty_content + short_content + long_content + duplicate_ids\n", "    quality_score = (total_records - quality_issues) / total_records * 100\n", "    \n", "    print(f\"\\n数据质量得分: {quality_score:.2f}%\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. 生成分析报告"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if df is not None:\n", "    # 生成分析报告\n", "    report = {\n", "        \"dataset_summary\": {\n", "            \"total_records\": len(df),\n", "            \"total_fields\": len(df.columns),\n", "            \"avg_content_length\": df['content_length'].mean(),\n", "            \"quality_score\": quality_score\n", "        },\n", "        \"distribution\": {\n", "            \"by_source\": df['source'].value_counts().to_dict(),\n", "            \"by_age_range\": df['age_range'].value_counts().to_dict(),\n", "            \"by_content_type\": df['content_type'].value_counts().to_dict()\n", "        },\n", "        \"content_analysis\": {\n", "            \"top_keywords\": dict(top_keywords[:10]) if 'top_keywords' in locals() else {},\n", "            \"top_words\": dict(top_words[:10]) if 'top_words' in locals() else {}\n", "        }\n", "    }\n", "    \n", "    # 保存报告\n", "    with open('../data/final/analysis_report.json', 'w', encoding='utf-8') as f:\n", "        json.dump(report, f, ensure_ascii=False, indent=2)\n", "    \n", "    print(\"分析报告已保存到 ../data/final/analysis_report.json\")\n", "    print(\"\\n=== 分析总结 ===\")\n", "    print(f\"数据集包含 {len(df)} 条记录\")\n", "    print(f\"平均内容长度: {df['content_length'].mean():.0f} 字符\")\n", "    print(f\"数据质量得分: {quality_score:.1f}%\")\n", "    print(f\"主要数据来源: {df['source'].mode().iloc[0]}\")\n", "    print(f\"覆盖年龄段: {len(df['age_range'].unique())} 个\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}