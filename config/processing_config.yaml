# 数据处理配置文件

# 数据收集配置
collection:
  # 爬虫设置
  crawler:
    delay: 1  # 请求间隔（秒）
    timeout: 30  # 超时时间（秒）
    retry_times: 3  # 重试次数
    user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
  
  # 文件处理
  file_processing:
    supported_formats: ["pdf", "docx", "txt", "html"]
    encoding: "utf-8"
    max_file_size: "50MB"

# 数据预处理配置
preprocessing:
  # 文本清洗
  text_cleaning:
    remove_html_tags: true
    remove_special_chars: false  # 保留必要的标点符号
    normalize_whitespace: true
    remove_empty_lines: true
  
  # 分词设置
  tokenization:
    tool: "jieba"  # 中文分词工具
    custom_dict: "data/custom_medical_dict.txt"
  
  # 内容过滤
  content_filtering:
    min_length: 50  # 最小字符数
    max_length: 5000  # 最大字符数
    remove_duplicates: true
    similarity_threshold: 0.85  # 相似度阈值

# 数据标准化配置
standardization:
  # 年龄分组标准
  age_groups:
    - name: "0-6months"
      display: "0-6个月"
      keywords: ["新生儿", "0-6个月", "婴儿期早期"]
    
    - name: "6-12months"
      display: "6-12个月"
      keywords: ["6-12个月", "婴儿期晚期", "咿呀学语"]
    
    - name: "1-2years"
      display: "1-2岁"
      keywords: ["1-2岁", "幼儿期早期", "词汇爆发期"]
    
    - name: "2-3years"
      display: "2-3岁"
      keywords: ["2-3岁", "幼儿期", "语法发展期"]
    
    - name: "3-6years"
      display: "3-6岁"
      keywords: ["3-6岁", "学龄前", "语言完善期"]

  # 内容分类标准
  content_categories:
    - name: "development_milestones"
      display: "发展里程碑"
      keywords: ["里程碑", "发展指标", "正常发展"]
    
    - name: "guidance_methods"
      display: "指导方法"
      keywords: ["方法", "技巧", "训练", "引导"]
    
    - name: "abnormal_signs"
      display: "异常表现"
      keywords: ["异常", "延迟", "障碍", "问题"]
    
    - name: "assessment_tools"
      display: "评估工具"
      keywords: ["评估", "测试", "量表", "检查"]

# QA生成配置
qa_generation:
  # 问题类型模板
  question_templates:
    milestone_questions:
      - "{age}的孩子语言发展应该达到什么水平？"
      - "{age}孩子的语言能力有哪些特点？"
      - "如何判断{age}孩子的语言发展是否正常？"
    
    guidance_questions:
      - "如何促进{age}孩子的语言发展？"
      - "{age}孩子语言发展迟缓怎么办？"
      - "家长如何在日常生活中引导{age}孩子说话？"
    
    problem_questions:
      - "{age}孩子不说话是什么原因？"
      - "如何识别{age}孩子的语言发展问题？"
      - "{age}孩子语言发展异常的表现有哪些？"

  # 答案生成规则
  answer_rules:
    structure:
      - "概述"
      - "具体表现"
      - "指导建议"
      - "注意事项"
    
    length:
      min_words: 100
      max_words: 500
    
    style:
      tone: "专业、温和、实用"
      avoid: ["绝对化表述", "医疗诊断", "商业推广"]

# 数据验证配置
validation:
  # 内容验证规则
  content_validation:
    required_fields: ["id", "source", "category", "age_range", "content"]
    age_range_values: ["0-6months", "6-12months", "1-2years", "2-3years", "3-6years"]
    source_values: ["government", "literature", "qa"]
    category_values: ["language_development", "cognitive_development", "social_development"]
  
  # 质量检查标准
  quality_checks:
    content_length:
      min: 50
      max: 5000
    
    professional_terms:
      check_consistency: true
      medical_dict: "data/medical_terms.txt"
    
    factual_accuracy:
      cross_reference: true
      authority_sources: ["WHO", "AAP", "中华医学会"]

# 输出格式配置
output:
  format: "jsonl"  # JSON Lines格式
  encoding: "utf-8"
  compression: false
  
  # 文件分割
  split_files:
    enabled: true
    max_records_per_file: 10000
    naming_pattern: "dataset_part_{:03d}.jsonl"
