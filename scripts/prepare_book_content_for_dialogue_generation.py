#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将OCR提取的书籍内容转换为对话生成脚本需要的格式
"""

import json
import re
from pathlib import Path
from typing import Dict, List
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BookContentProcessor:
    """书籍内容处理器，用于准备对话生成的输入数据"""
    
    def __init__(self):
        self.ocr_output_dir = Path("data/raw/literature/ocr_output")
        self.output_dir = Path("data/dialogue_generation")
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def load_ocr_content(self) -> Dict:
        """加载OCR提取的完整内容"""
        logger.info("加载OCR提取的完整内容...")
        
        pages_file = self.ocr_output_dir / "ocr_all_pages.json"
        if not pages_file.exists():
            logger.error("OCR页面文件不存在")
            return {}
        
        with open(pages_file, 'r', encoding='utf-8') as f:
            pages_data = json.load(f)
        
        logger.info(f"加载了 {len(pages_data)} 页内容")
        return pages_data
    
    def extract_meaningful_segments(self, pages_data: Dict) -> List[Dict]:
        """提取有意义的文本段落"""
        logger.info("提取有意义的文本段落...")
        
        segments = []
        
        # 按页面顺序处理
        for page_key in sorted(pages_data.keys(), key=lambda x: int(x.split('_')[1])):
            page_data = pages_data[page_key]
            content = page_data['cleaned_text']
            page_num = page_data['page_number']
            
            # 跳过太短的内容
            if len(content) < 200:
                continue
            
            # 按段落分割内容
            paragraphs = self._split_into_paragraphs(content)
            
            for i, paragraph in enumerate(paragraphs):
                if len(paragraph) >= 300:  # 只保留足够长的段落
                    # 确定段落主题
                    title = self._extract_title_from_paragraph(paragraph, page_num, i)
                    
                    # 确定年龄段
                    age_range = self._determine_age_range(paragraph)
                    
                    # 确定内容类型
                    content_type = self._determine_content_type(paragraph)
                    
                    segment = {
                        "desc": paragraph,
                        "title": title,
                        "page_number": page_num,
                        "age_range": age_range,
                        "content_type": content_type,
                        "source": "0-3岁婴幼儿语言发展与教育"
                    }
                    
                    segments.append(segment)
        
        logger.info(f"提取了 {len(segments)} 个有意义的文本段落")
        return segments
    
    def _split_into_paragraphs(self, content: str) -> List[str]:
        """将内容分割为段落"""
        # 按照句号、问号、感叹号分割，但保持一定的长度
        sentences = re.split(r'[。！？]', content)
        paragraphs = []
        current_paragraph = ""
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
            
            # 如果当前段落加上新句子后长度合适，就添加
            if len(current_paragraph + sentence) < 800:
                current_paragraph += sentence + "。"
            else:
                # 如果当前段落已经足够长，就保存它
                if len(current_paragraph) >= 300:
                    paragraphs.append(current_paragraph.strip())
                # 开始新段落
                current_paragraph = sentence + "。"
        
        # 添加最后一个段落
        if len(current_paragraph) >= 300:
            paragraphs.append(current_paragraph.strip())
        
        return paragraphs
    
    def _extract_title_from_paragraph(self, paragraph: str, page_num: int, segment_idx: int) -> str:
        """从段落中提取标题"""
        # 查找段落开头的标题模式
        title_patterns = [
            r'^第[一二三四五六七八九十\d]+章[^\n。]{0,30}',
            r'^第[一二三四五六七八九十\d]+节[^\n。]{0,30}',
            r'^\d+\.\d+[^\n。]{5,30}',
            r'^[一二三四五六七八九十]、[^\n。]{5,30}',
            r'^（[一二三四五六七八九十]）[^\n。]{5,30}',
        ]
        
        for pattern in title_patterns:
            match = re.search(pattern, paragraph)
            if match:
                return match.group().strip()
        
        # 如果没有找到明显的标题，从内容中提取关键概念
        key_concepts = [
            '语言发展', '语言获得', '语言教育', '语言训练', '语言游戏',
            '亲子交流', '语言环境', '发展理论', '评价指标', '指导方法'
        ]
        
        for concept in key_concepts:
            if concept in paragraph:
                return f"{concept}相关内容（第{page_num}页）"
        
        # 默认标题
        return f"婴幼儿语言发展内容（第{page_num}页-{segment_idx}）"
    
    def _determine_age_range(self, content: str) -> str:
        """确定内容适用的年龄段"""
        age_indicators = {
            '0-6个月': ['0-3个月', '4-6个月', '新生儿', '婴儿期早期', '咕咕声', '元音', '哭声'],
            '6-12个月': ['7-9个月', '10-12个月', '婴儿期晚期', '咿呀学语', 'mama', 'baba'],
            '1-2岁': ['13-18个月', '19-24个月', '1-2岁', '幼儿期早期', '双词句', '词汇爆发'],
            '2-3岁': ['25-30个月', '31-36个月', '2-3岁', '幼儿期', '语法发展', '复杂句'],
            '0-3岁': ['0-3岁', '婴幼儿', '早期发展', '语言获得', '语言教育']
        }
        
        # 计算每个年龄段的匹配分数
        age_scores = {}
        for age_range, indicators in age_indicators.items():
            score = sum(1 for indicator in indicators if indicator in content)
            if score > 0:
                age_scores[age_range] = score
        
        # 返回得分最高的年龄段
        if age_scores:
            return max(age_scores.items(), key=lambda x: x[1])[0]
        
        return '0-3岁'  # 默认返回
    
    def _determine_content_type(self, content: str) -> str:
        """确定内容类型"""
        type_indicators = {
            '理论知识': ['理论', '学说', '观点', '假设', '研究', '学派'],
            '发展里程碑': ['评价指标', '发展特点', '里程碑', '发展水平', '能力表现'],
            '实践指导': ['方法', '指导', '训练', '活动', '游戏', '技巧', '策略'],
            '问题解决': ['问题', '异常', '障碍', '困难', '解决', '改善', '干预']
        }
        
        # 计算每种类型的匹配分数
        type_scores = {}
        for content_type, indicators in type_indicators.items():
            score = sum(1 for indicator in indicators if indicator in content)
            if score > 0:
                type_scores[content_type] = score
        
        # 返回得分最高的类型
        if type_scores:
            return max(type_scores.items(), key=lambda x: x[1])[0]
        
        return '一般内容'  # 默认返回
    
    def create_enhanced_segments(self, segments: List[Dict]) -> List[Dict]:
        """创建增强的文本段落，包含更多上下文信息"""
        logger.info("创建增强的文本段落...")
        
        enhanced_segments = []
        
        for segment in segments:
            # 原始段落
            enhanced_segments.append(segment)
            
            # 如果是理论内容，创建应用导向的变体
            if segment['content_type'] == '理论知识':
                enhanced_segment = segment.copy()
                enhanced_segment['desc'] = f"基于以下理论知识，请解释其在婴幼儿语言教育实践中的应用：\n\n{segment['desc']}"
                enhanced_segment['title'] = f"{segment['title']}的实践应用"
                enhanced_segments.append(enhanced_segment)
            
            # 如果是发展里程碑，创建评估导向的变体
            elif segment['content_type'] == '发展里程碑':
                enhanced_segment = segment.copy()
                enhanced_segment['desc'] = f"以下是婴幼儿语言发展的评价标准，请基于此内容回答相关的评估和指导问题：\n\n{segment['desc']}"
                enhanced_segment['title'] = f"{segment['title']}评估指导"
                enhanced_segments.append(enhanced_segment)
            
            # 如果是实践指导，创建问题解决导向的变体
            elif segment['content_type'] == '实践指导':
                enhanced_segment = segment.copy()
                enhanced_segment['desc'] = f"以下是婴幼儿语言发展的实践指导方法，请基于此内容提供具体的操作建议：\n\n{segment['desc']}"
                enhanced_segment['title'] = f"{segment['title']}操作指南"
                enhanced_segments.append(enhanced_segment)
        
        logger.info(f"创建了 {len(enhanced_segments)} 个增强文本段落")
        return enhanced_segments
    
    def save_reference_data(self, segments: List[Dict]):
        """保存参考数据为对话生成脚本需要的格式"""
        logger.info("保存参考数据...")
        
        # 保存为JSONL格式
        output_file = self.output_dir / "book_content_references.jsonl"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            for segment in segments:
                f.write(json.dumps(segment, ensure_ascii=False) + '\n')
        
        logger.info(f"参考数据已保存到 {output_file}")
        
        # 生成统计报告
        self._generate_reference_report(segments)
        
        return str(output_file)
    
    def _generate_reference_report(self, segments: List[Dict]):
        """生成参考数据统计报告"""
        report = {
            'total_segments': len(segments),
            'by_age_range': {},
            'by_content_type': {},
            'avg_length': 0,
            'length_distribution': {
                'min': 0,
                'max': 0,
                'median': 0
            }
        }
        
        lengths = []
        
        for segment in segments:
            # 按年龄段统计
            age_range = segment.get('age_range', 'unknown')
            report['by_age_range'][age_range] = report['by_age_range'].get(age_range, 0) + 1
            
            # 按内容类型统计
            content_type = segment.get('content_type', 'unknown')
            report['by_content_type'][content_type] = report['by_content_type'].get(content_type, 0) + 1
            
            # 长度统计
            length = len(segment.get('desc', ''))
            lengths.append(length)
        
        if lengths:
            report['avg_length'] = sum(lengths) / len(lengths)
            report['length_distribution'] = {
                'min': min(lengths),
                'max': max(lengths),
                'median': sorted(lengths)[len(lengths)//2]
            }
        
        # 保存报告
        report_file = self.output_dir / "reference_data_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"统计报告已保存到 {report_file}")
        
        # 打印摘要
        print(f"\n=== 参考数据准备完成 ===")
        print(f"总段落数: {report['total_segments']}")
        print(f"平均长度: {report['avg_length']:.0f} 字符")
        print(f"长度范围: {report['length_distribution']['min']}-{report['length_distribution']['max']} 字符")
        
        print(f"\n按年龄段分布:")
        for age_range, count in report['by_age_range'].items():
            print(f"  {age_range}: {count}")
        
        print(f"\n按内容类型分布:")
        for content_type, count in report['by_content_type'].items():
            print(f"  {content_type}: {count}")
    
    def process_all(self) -> str:
        """执行完整的处理流程"""
        logger.info("开始处理书籍内容为对话生成格式...")
        
        # 1. 加载OCR内容
        pages_data = self.load_ocr_content()
        if not pages_data:
            return None
        
        # 2. 提取有意义的段落
        segments = self.extract_meaningful_segments(pages_data)
        
        # 3. 创建增强段落
        enhanced_segments = self.create_enhanced_segments(segments)
        
        # 4. 保存参考数据
        output_file = self.save_reference_data(enhanced_segments)
        
        return output_file

def main():
    """主函数"""
    processor = BookContentProcessor()
    output_file = processor.process_all()
    
    if output_file:
        print(f"\n🎉 书籍内容处理完成！")
        print(f"参考数据文件: {output_file}")
        print(f"\n下一步可以使用以下命令生成对话数据集:")
        print(f"python parallel_generate-refgpt-fact_副本.py \\")
        print(f"  --reference_filepaths {output_file} \\")
        print(f"  --save_filepath data/dialogue_generation/generated_dialogues.jsonl \\")
        print(f"  --api_config api_config.jsonl \\")
        print(f"  --language zh \\")
        print(f"  --num_chat_to_generate 100 \\")
        print(f"  --model qwen-turbo")
    else:
        logger.error("书籍内容处理失败")

if __name__ == "__main__":
    main()
