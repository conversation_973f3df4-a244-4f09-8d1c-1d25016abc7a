#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
婴幼儿语言发展指导QA问答对生成脚本
"""

import json
import yaml
import random
from typing import Dict, List, Tuple
import logging
from pathlib import Path
import uuid

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class QAGenerator:
    """QA问答对生成器"""
    
    def __init__(self, config_path: str = "config/data_sources.yaml"):
        """初始化QA生成器"""
        self.config = self._load_config(config_path)
        self.qa_config = self.config.get('qa_sources', {})
        
        # 创建输出目录
        Path("data/raw/qa_pairs").mkdir(parents=True, exist_ok=True)
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def generate_milestone_qa(self) -> List[Dict]:
        """生成发展里程碑相关的QA对"""
        logger.info("生成发展里程碑QA对...")
        
        qa_pairs = []
        age_groups = self.qa_config.get('age_groups', [])
        
        for age_group in age_groups:
            age_name = age_group['name']
            focus_areas = age_group['focus_areas']
            
            # 为每个年龄段生成多个QA对
            qa_pairs.extend(self._generate_age_specific_qa(age_name, focus_areas))
        
        return qa_pairs
    
    def _generate_age_specific_qa(self, age_name: str, focus_areas: List[str]) -> List[Dict]:
        """为特定年龄段生成QA对"""
        qa_pairs = []
        
        # 发展里程碑问题
        milestone_questions = [
            f"{age_name}的孩子语言发展应该达到什么水平？",
            f"{age_name}孩子的语言能力有哪些特点？",
            f"如何判断{age_name}孩子的语言发展是否正常？",
            f"{age_name}孩子的语言发展里程碑有哪些？"
        ]
        
        for question in milestone_questions:
            answer = self._generate_milestone_answer(age_name, focus_areas)
            qa_pair = {
                "id": str(uuid.uuid4()),
                "source": "qa",
                "category": "language_development",
                "content_type": "qa_pair",
                "age_range": self._normalize_age_range(age_name),
                "question": question,
                "answer": answer,
                "metadata": {
                    "question_type": "milestone",
                    "focus_areas": focus_areas,
                    "difficulty": "basic"
                }
            }
            qa_pairs.append(qa_pair)
        
        # 指导方法问题
        guidance_questions = [
            f"如何促进{age_name}孩子的语言发展？",
            f"家长如何在日常生活中引导{age_name}孩子说话？",
            f"{age_name}孩子语言发展的最佳训练方法是什么？"
        ]
        
        for question in guidance_questions:
            answer = self._generate_guidance_answer(age_name, focus_areas)
            qa_pair = {
                "id": str(uuid.uuid4()),
                "source": "qa",
                "category": "language_development",
                "content_type": "qa_pair",
                "age_range": self._normalize_age_range(age_name),
                "question": question,
                "answer": answer,
                "metadata": {
                    "question_type": "guidance",
                    "focus_areas": focus_areas,
                    "difficulty": "intermediate"
                }
            }
            qa_pairs.append(qa_pair)
        
        # 问题识别问题
        problem_questions = [
            f"{age_name}孩子不说话是什么原因？",
            f"如何识别{age_name}孩子的语言发展问题？",
            f"{age_name}孩子语言发展异常的表现有哪些？"
        ]
        
        for question in problem_questions:
            answer = self._generate_problem_answer(age_name, focus_areas)
            qa_pair = {
                "id": str(uuid.uuid4()),
                "source": "qa",
                "category": "language_development",
                "content_type": "qa_pair",
                "age_range": self._normalize_age_range(age_name),
                "question": question,
                "answer": answer,
                "metadata": {
                    "question_type": "problem_identification",
                    "focus_areas": focus_areas,
                    "difficulty": "advanced"
                }
            }
            qa_pairs.append(qa_pair)
        
        return qa_pairs
    
    def _generate_milestone_answer(self, age_name: str, focus_areas: List[str]) -> str:
        """生成发展里程碑答案"""
        # 根据年龄段生成相应的里程碑答案
        milestone_templates = {
            "0-6个月": {
                "intro": "0-6个月是婴儿语言发展的基础阶段，主要特点包括：",
                "milestones": [
                    "能够发出各种声音，如咕咕声、啊啊声",
                    "对熟悉的声音有反应，会转头寻找声源",
                    "开始模仿成人的面部表情和声音",
                    "哭声有不同的含义，表达不同需求",
                    "6个月左右开始咿呀学语，发出重复的音节"
                ]
            },
            "6-12个月": {
                "intro": "6-12个月是婴儿语言发展的重要时期，主要特点包括：",
                "milestones": [
                    "咿呀学语更加频繁，能发出'ba-ba'、'ma-ma'等音节",
                    "开始理解简单的词汇，如'不'、'再见'",
                    "能够通过手势和声音表达需求",
                    "对自己的名字有反应",
                    "12个月左右可能说出第一个有意义的词"
                ]
            },
            "1-2岁": {
                "intro": "1-2岁是幼儿词汇快速发展期，主要特点包括：",
                "milestones": [
                    "词汇量从几个词增长到50-200个词",
                    "开始使用双词句，如'妈妈抱'、'要水'",
                    "能理解简单的指令和问题",
                    "开始使用代词'我'、'你'",
                    "语音逐渐清晰，但仍有很多发音不准确"
                ]
            },
            "2-3岁": {
                "intro": "2-3岁是幼儿语法发展的关键期，主要特点包括：",
                "milestones": [
                    "词汇量达到300-1000个词",
                    "能说出3-4个词的句子",
                    "开始使用语法规则，如复数、过去式",
                    "能进行简单的对话",
                    "开始问'为什么'、'是什么'等问题"
                ]
            },
            "3-6岁": {
                "intro": "3-6岁是幼儿语言能力完善期，主要特点包括：",
                "milestones": [
                    "词汇量达到2000-4000个词",
                    "能说出复杂的句子，使用各种语法结构",
                    "能够叙述完整的故事",
                    "理解抽象概念和比喻",
                    "语音基本准确，能被陌生人理解"
                ]
            }
        }
        
        template = milestone_templates.get(age_name, milestone_templates["1-2岁"])
        
        answer = template["intro"] + "\n\n"
        for i, milestone in enumerate(template["milestones"], 1):
            answer += f"{i}. {milestone}\n"
        
        answer += f"\n需要注意的是，每个孩子的发展速度不同，以上标准仅供参考。如果发现孩子的语言发展明显滞后，建议及时咨询专业医生或语言治疗师。"
        
        return answer
    
    def _generate_guidance_answer(self, age_name: str, focus_areas: List[str]) -> str:
        """生成指导方法答案"""
        guidance_templates = {
            "0-6个月": [
                "多与宝宝说话，即使他们还不会回应",
                "模仿宝宝的声音，鼓励他们发声",
                "唱歌给宝宝听，培养语言节奏感",
                "读简单的图画书，指着图片说话",
                "回应宝宝的每一个声音和表情"
            ],
            "6-12个月": [
                "重复宝宝发出的音节，如'ba-ba'、'ma-ma'",
                "使用简单的词汇描述日常活动",
                "玩躲猫猫游戏，增加互动乐趣",
                "指着物品说出名称，建立词汇联系",
                "鼓励宝宝模仿手势和声音"
            ],
            "1-2岁": [
                "扩展孩子的话语，如孩子说'车'，你可以说'红色的车'",
                "读故事书，指着图片提问",
                "唱儿歌，教简单的手指游戏",
                "在日常活动中不断说话，如'现在我们要洗手'",
                "给孩子充分的时间回应，不要急于纠正"
            ],
            "2-3岁": [
                "鼓励孩子描述看到的事物",
                "提开放性问题，如'你觉得怎么样？'",
                "一起看图书，让孩子讲故事",
                "玩角色扮演游戏，增加语言使用机会",
                "耐心听孩子说话，给予积极回应"
            ],
            "3-6岁": [
                "鼓励孩子表达想法和感受",
                "一起讨论故事情节和人物",
                "教孩子新词汇，解释词汇含义",
                "鼓励孩子提问，耐心回答",
                "创造丰富的语言环境，多样化对话"
            ]
        }
        
        methods = guidance_templates.get(age_name, guidance_templates["1-2岁"])
        
        answer = f"促进{age_name}孩子语言发展的有效方法包括：\n\n"
        for i, method in enumerate(methods, 1):
            answer += f"{i}. {method}\n"
        
        answer += "\n记住，语言发展需要时间和耐心。创造一个充满爱和支持的语言环境，是促进孩子语言发展的关键。"
        
        return answer
    
    def _generate_problem_answer(self, age_name: str, focus_areas: List[str]) -> str:
        """生成问题识别答案"""
        answer = f"对于{age_name}的孩子，需要关注以下可能的语言发展问题：\n\n"
        
        # 根据年龄段添加具体的问题表现
        if "0-6个月" in age_name:
            answer += "可能的异常表现：\n"
            answer += "1. 对声音没有反应\n"
            answer += "2. 不会发出咕咕声或其他声音\n"
            answer += "3. 不会模仿面部表情\n"
        elif "6-12个月" in age_name:
            answer += "可能的异常表现：\n"
            answer += "1. 12个月时仍不会咿呀学语\n"
            answer += "2. 对自己的名字没有反应\n"
            answer += "3. 不会用手势表达需求\n"
        elif "1-2岁" in age_name:
            answer += "可能的异常表现：\n"
            answer += "1. 18个月时词汇量少于10个词\n"
            answer += "2. 2岁时不会说双词句\n"
            answer += "3. 不理解简单指令\n"
        elif "2-3岁" in age_name:
            answer += "可能的异常表现：\n"
            answer += "1. 3岁时词汇量少于200个词\n"
            answer += "2. 不会说3个词以上的句子\n"
            answer += "3. 语言难以被家人理解\n"
        else:  # 3-6岁
            answer += "可能的异常表现：\n"
            answer += "1. 语言表达不清楚，陌生人难以理解\n"
            answer += "2. 不会叙述简单故事\n"
            answer += "3. 语法错误较多\n"
        
        answer += "\n如果发现以上问题，建议：\n"
        answer += "1. 及时咨询儿科医生或语言治疗师\n"
        answer += "2. 进行专业的语言发展评估\n"
        answer += "3. 根据专业建议制定干预计划\n"
        answer += "4. 家庭配合进行语言训练\n"
        
        answer += "\n早期发现和干预对语言发展问题的改善非常重要。"
        
        return answer
    
    def _normalize_age_range(self, age_name: str) -> str:
        """标准化年龄范围"""
        age_mapping = {
            "0-6个月": "0-6months",
            "6-12个月": "6-12months", 
            "1-2岁": "1-2years",
            "2-3岁": "2-3years",
            "3-6岁": "3-6years"
        }
        return age_mapping.get(age_name, "unknown")
    
    def generate_all_qa_pairs(self) -> List[Dict]:
        """生成所有QA对"""
        logger.info("开始生成QA问答对...")
        
        all_qa_pairs = []
        
        # 生成里程碑相关QA
        milestone_qa = self.generate_milestone_qa()
        all_qa_pairs.extend(milestone_qa)
        
        logger.info(f"共生成 {len(all_qa_pairs)} 个QA对")
        
        return all_qa_pairs
    
    def save_qa_pairs(self, qa_pairs: List[Dict]):
        """保存QA对到文件"""
        output_file = "data/raw/qa_pairs/generated_qa.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(qa_pairs, f, ensure_ascii=False, indent=2)
        
        logger.info(f"QA对已保存到 {output_file}")
        
        # 生成统计报告
        self._generate_qa_report(qa_pairs)
    
    def _generate_qa_report(self, qa_pairs: List[Dict]):
        """生成QA统计报告"""
        report = {
            "total_qa_pairs": len(qa_pairs),
            "by_age_range": {},
            "by_question_type": {},
            "by_difficulty": {}
        }
        
        for qa in qa_pairs:
            # 按年龄段统计
            age_range = qa.get('age_range', 'unknown')
            report['by_age_range'][age_range] = report['by_age_range'].get(age_range, 0) + 1
            
            # 按问题类型统计
            question_type = qa.get('metadata', {}).get('question_type', 'unknown')
            report['by_question_type'][question_type] = report['by_question_type'].get(question_type, 0) + 1
            
            # 按难度统计
            difficulty = qa.get('metadata', {}).get('difficulty', 'unknown')
            report['by_difficulty'][difficulty] = report['by_difficulty'].get(difficulty, 0) + 1
        
        with open("data/raw/qa_pairs/qa_report.json", 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info("QA统计报告已生成")

def main():
    """主函数"""
    generator = QAGenerator()
    qa_pairs = generator.generate_all_qa_pairs()
    generator.save_qa_pairs(qa_pairs)

if __name__ == "__main__":
    main()
