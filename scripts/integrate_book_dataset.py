#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
整合基于书籍内容的数据集到主数据集
"""

import json
import logging
from pathlib import Path
from typing import List, Dict

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DatasetIntegrator:
    """数据集整合器"""
    
    def __init__(self):
        self.book_dataset_path = Path("data/raw/literature/book_dataset/book_based_dataset.json")
        self.original_qa_path = Path("data/raw/qa_pairs/generated_qa.json")
        self.original_gov_path = Path("data/raw/government")
        self.original_lit_path = Path("data/raw/literature/academic_literature.json")
        
        self.output_dir = Path("data/raw/integrated")
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def load_book_dataset(self) -> List[Dict]:
        """加载基于书籍内容的数据集"""
        logger.info("加载基于书籍内容的数据集...")
        
        if not self.book_dataset_path.exists():
            logger.error("书籍数据集文件不存在")
            return []
        
        with open(self.book_dataset_path, 'r', encoding='utf-8') as f:
            book_data = json.load(f)
        
        logger.info(f"加载了 {len(book_data)} 条书籍数据")
        return book_data
    
    def load_original_datasets(self) -> List[Dict]:
        """加载原始数据集"""
        logger.info("加载原始数据集...")
        
        all_original_data = []
        
        # 加载QA数据
        if self.original_qa_path.exists():
            with open(self.original_qa_path, 'r', encoding='utf-8') as f:
                qa_data = json.load(f)
                all_original_data.extend(qa_data)
                logger.info(f"加载了 {len(qa_data)} 条QA数据")
        
        # 加载政府数据
        if self.original_gov_path.exists():
            for gov_file in self.original_gov_path.glob("*.json"):
                with open(gov_file, 'r', encoding='utf-8') as f:
                    gov_data = json.load(f)
                    if isinstance(gov_data, list):
                        all_original_data.extend(gov_data)
                    else:
                        all_original_data.append(gov_data)
        
        # 加载文献数据
        if self.original_lit_path.exists():
            with open(self.original_lit_path, 'r', encoding='utf-8') as f:
                lit_data = json.load(f)
                all_original_data.extend(lit_data)
                logger.info(f"加载了 {len(lit_data)} 条文献数据")
        
        logger.info(f"总共加载了 {len(all_original_data)} 条原始数据")
        return all_original_data
    
    def enhance_qa_with_book_knowledge(self, book_data: List[Dict]) -> List[Dict]:
        """基于书籍知识增强QA问答对"""
        logger.info("基于书籍知识生成增强的QA问答对...")
        
        enhanced_qa = []
        
        # 基于书籍中的发展评价指标生成更多QA
        for item in book_data:
            if item['content_type'] == 'development_milestones':
                title = item['title']
                content = item['content']
                age_range = item['age_range']
                
                if '0-3个月' in title:
                    enhanced_qa.append({
                        'id': f"enhanced_qa_{len(enhanced_qa)+1}",
                        'source': 'qa',
                        'category': 'language_development',
                        'age_range': age_range,
                        'content_type': 'qa_pair',
                        'title': '如何评估0-3个月婴儿的语言发展？',
                        'content': f'评估0-3个月婴儿的语言发展需要从三个方面观察：\n\n1. 语言理解能力：观察婴儿是否对说话声敏感，特别是对高音的反应；是否会因听到新异声音而停止正在做的动作；是否开始将声音和形象联系起来。\n\n2. 语言表达能力：观察婴儿是否会对养育者的逗引发出"咕咕"声；是否会发简单的元音如"a"、"o"、"e"音；听到养育者声音时是否会露出笑脸。\n\n3. 语言运用能力：观察婴儿是否用表情、动作、自发声音表示身体情绪状态；不同类型的哭声是否代表不同意思。\n\n这些都是0-3个月婴儿语言发展的重要指标。',
                        'metadata': {
                            'question_type': 'assessment',
                            'difficulty': 'intermediate',
                            'source_book': '0-3岁婴幼儿语言发展与教育',
                            'enhanced': True
                        }
                    })
                
                elif '4-6个月' in title:
                    enhanced_qa.append({
                        'id': f"enhanced_qa_{len(enhanced_qa)+1}",
                        'source': 'qa',
                        'category': 'language_development',
                        'age_range': age_range,
                        'content_type': 'qa_pair',
                        'title': '4-6个月婴儿语言发展的关键表现是什么？',
                        'content': f'4-6个月婴儿语言发展的关键表现包括：\n\n语言理解方面：\n- 养育者说话时能停止哭泣，表明开始理解语言的安抚作用\n- 能够持续注意并寻找声音来源，听觉定位能力发展\n- 能区别不同的语调、语气、音色变化并做出相应反应\n\n语言表达方面：\n- 口中经常发出成串语音，如"boboba"、"dododo"等\n- 开始发出长音和尖叫声\n- 咿呀学语增多，开始发辅音如"d"、"n"、"m"等\n\n语言运用方面：\n- 开始注意看图书，虽然会放进嘴里\n- 主动用语音吸引养育者注意\n\n这个阶段是婴儿从被动接受语言刺激向主动语言交流转变的重要时期。',
                        'metadata': {
                            'question_type': 'milestone',
                            'difficulty': 'basic',
                            'source_book': '0-3岁婴幼儿语言发展与教育',
                            'enhanced': True
                        }
                    })
                
                elif '7-9个月' in title:
                    enhanced_qa.append({
                        'id': f"enhanced_qa_{len(enhanced_qa)+1}",
                        'source': 'qa',
                        'category': 'language_development',
                        'age_range': age_range,
                        'content_type': 'qa_pair',
                        'title': '7-9个月婴儿开始理解语言了吗？',
                        'content': f'是的，7-9个月是婴儿语言理解能力快速发展的时期：\n\n语言理解能力：\n- 听得懂自己的名字，听到叫名字时会扭头看叫的人\n- 对养育者的"不"或"别碰它"等要求能作出正确反应\n- 能够识别家人的名字和一些熟悉的物体名称\n- 会试着翻书，喜欢以前听过的故事\n\n语言表达能力：\n- 会用舌头、嘴唇发出各种声音\n- 能反复发出"mama"、"baba"等音节，但还没有特定所指\n- 发出连续音，出现特定的语音模式\n\n语言运用能力：\n- 能够和养育者玩一些语言游戏\n- 努力模仿别人发出的语音\n- 开始用动作进行交流，如挥手表示再见\n\n这个阶段婴儿的语言理解能力明显超过表达能力，这是正常的发展规律。',
                        'metadata': {
                            'question_type': 'milestone',
                            'difficulty': 'basic',
                            'source_book': '0-3岁婴幼儿语言发展与教育',
                            'enhanced': True
                        }
                    })
                
                elif '10-12个月' in title:
                    enhanced_qa.append({
                        'id': f"enhanced_qa_{len(enhanced_qa)+1}",
                        'source': 'qa',
                        'category': 'language_development',
                        'age_range': age_range,
                        'content_type': 'qa_pair',
                        'title': '10-12个月婴儿会说第一个词吗？',
                        'content': f'10-12个月是婴儿可能说出第一个有意义词汇的重要时期：\n\n语言理解能力：\n- 理解简单的命令性语言，如"到这儿来"、"坐下"等\n- 能听懂关于吃、玩、家里人名字以及常用物品名称\n- 能按要求指向自己的耳朵、眼睛和鼻子\n\n语言表达能力：\n- 可能说出第一个有意义的词，通常是"mama"或"baba"\n- 开始有意识地使用特定音节指代特定事物\n- 发音虽然不够清晰，但有明确的指向性\n\n语言运用能力：\n- 用动作表示同意或不同意（点头摇头）\n- 可以模仿非语言声音，如咳嗽声\n- 开始理解语言的社交功能\n\n需要注意的是，每个婴儿的发展速度不同，有些可能早一些，有些可能晚一些，这都是正常的。',
                        'metadata': {
                            'question_type': 'milestone',
                            'difficulty': 'basic',
                            'source_book': '0-3岁婴幼儿语言发展与教育',
                            'enhanced': True
                        }
                    })
        
        # 基于理论知识生成实践指导QA
        for item in book_data:
            if item['content_type'] == 'knowledge' and '社会交互作用理论' in item['title']:
                enhanced_qa.append({
                    'id': f"enhanced_qa_{len(enhanced_qa)+1}",
                    'source': 'qa',
                    'category': 'language_development',
                    'age_range': '0-3years',
                    'content_type': 'qa_pair',
                    'title': '如何运用社会交互作用理论指导婴幼儿语言发展？',
                    'content': f'社会交互作用理论为婴幼儿语言发展提供了重要的指导原则：\n\n1. 创造丰富的社会交往环境：\n- 多与婴幼儿进行面对面的交流\n- 鼓励婴幼儿与不同的人接触\n- 在日常生活中增加语言互动机会\n\n2. 提供适宜的语言输入：\n- 使用"儿童指向语"(CDS)，语调高、语速慢、重复多\n- 根据孩子的语言水平调整语言复杂度\n- 对孩子的发音尝试给予积极回应\n\n3. 重视孩子的主体作用：\n- 观察孩子的语言发展水平和兴趣\n- 跟随孩子的注意力和兴趣点进行交流\n- 给孩子充分的表达时间和机会\n\n4. 强化模仿和互动：\n- 适当重复和扩展孩子的话语\n- 通过游戏和日常活动进行语言练习\n- 创造需要语言交流的情境\n\n这种综合性的方法比单纯的语言训练更有效。',
                    'metadata': {
                        'question_type': 'guidance',
                        'difficulty': 'advanced',
                        'source_book': '0-3岁婴幼儿语言发展与教育',
                        'enhanced': True
                    }
                })
        
        logger.info(f"生成了 {len(enhanced_qa)} 个增强的QA问答对")
        return enhanced_qa
    
    def integrate_all_datasets(self) -> List[Dict]:
        """整合所有数据集"""
        logger.info("开始整合所有数据集...")
        
        # 加载各种数据
        book_data = self.load_book_dataset()
        original_data = self.load_original_datasets()
        enhanced_qa = self.enhance_qa_with_book_knowledge(book_data)
        
        # 合并所有数据
        integrated_dataset = book_data + original_data + enhanced_qa
        
        # 去重（基于标题）
        seen_titles = set()
        unique_dataset = []
        
        for item in integrated_dataset:
            title = item.get('title', '')
            if title not in seen_titles:
                seen_titles.add(title)
                unique_dataset.append(item)
        
        logger.info(f"整合后数据集包含 {len(unique_dataset)} 条唯一记录")
        return unique_dataset
    
    def save_integrated_dataset(self, dataset: List[Dict]):
        """保存整合后的数据集"""
        logger.info("保存整合后的数据集...")
        
        # 保存为JSON格式
        output_file = self.output_dir / "integrated_dataset.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(dataset, f, ensure_ascii=False, indent=2)
        
        # 生成详细统计报告
        report = {
            'total_records': len(dataset),
            'by_source': {},
            'by_content_type': {},
            'by_age_range': {},
            'enhanced_records': 0,
            'book_based_records': 0
        }
        
        for item in dataset:
            # 按来源统计
            source = item.get('source', 'unknown')
            report['by_source'][source] = report['by_source'].get(source, 0) + 1
            
            # 按内容类型统计
            content_type = item.get('content_type', 'unknown')
            report['by_content_type'][content_type] = report['by_content_type'].get(content_type, 0) + 1
            
            # 按年龄段统计
            age_range = item.get('age_range', 'unknown')
            report['by_age_range'][age_range] = report['by_age_range'].get(age_range, 0) + 1
            
            # 统计增强记录
            if item.get('metadata', {}).get('enhanced'):
                report['enhanced_records'] += 1
            
            # 统计书籍记录
            if item.get('metadata', {}).get('book_title'):
                report['book_based_records'] += 1
        
        # 保存报告
        report_file = self.output_dir / "integration_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"整合数据集已保存到 {output_file}")
        logger.info(f"统计报告已保存到 {report_file}")
        
        return report

def main():
    """主函数"""
    integrator = DatasetIntegrator()
    
    # 整合数据集
    integrated_dataset = integrator.integrate_all_datasets()
    
    if integrated_dataset:
        # 保存整合后的数据集
        report = integrator.save_integrated_dataset(integrated_dataset)
        
        # 显示结果
        print(f"\n=== 数据集整合完成 ===")
        print(f"总记录数: {report['total_records']}")
        print(f"基于书籍的记录: {report['book_based_records']}")
        print(f"增强的记录: {report['enhanced_records']}")
        
        print(f"\n按来源分布:")
        for source, count in report['by_source'].items():
            print(f"  {source}: {count}")
        
        print(f"\n按内容类型分布:")
        for content_type, count in report['by_content_type'].items():
            print(f"  {content_type}: {count}")
        
        print(f"\n按年龄段分布:")
        for age_range, count in report['by_age_range'].items():
            print(f"  {age_range}: {count}")
        
        # 显示一些样本
        print(f"\n=== 数据样本 ===")
        book_samples = [item for item in integrated_dataset if item.get('metadata', {}).get('book_title')][:3]
        for i, item in enumerate(book_samples):
            print(f"\n{i+1}. {item['title']}")
            print(f"   来源: {item['source']} | 类型: {item['content_type']} | 年龄: {item['age_range']}")
            print(f"   内容: {item['content'][:100]}...")
    
    else:
        logger.error("数据集整合失败")

if __name__ == "__main__":
    main()
