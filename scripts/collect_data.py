#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
婴幼儿语言发展指导数据收集脚本
"""

import os
import json
import yaml
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import time
import logging
from pathlib import Path
from typing import Dict, List, Optional
import pandas as pd

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/data_collection.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DataCollector:
    """数据收集器"""
    
    def __init__(self, config_path: str = "config/data_sources.yaml"):
        """初始化数据收集器"""
        self.config = self._load_config(config_path)
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 创建输出目录
        os.makedirs("data/raw/government", exist_ok=True)
        os.makedirs("data/raw/literature", exist_ok=True)
        os.makedirs("logs", exist_ok=True)
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def collect_government_data(self):
        """收集政府官方数据"""
        logger.info("开始收集政府官方数据...")
        
        gov_sources = self.config.get('government_sources', {})
        collected_data = []
        
        for source_key, source_info in gov_sources.items():
            logger.info(f"收集来源: {source_info['name']}")
            
            # 这里需要根据具体网站结构实现爬虫逻辑
            # 由于政府网站结构复杂，这里提供框架代码
            try:
                data = self._collect_from_government_source(source_info)
                collected_data.extend(data)
                
                # 保存到文件
                output_file = f"data/raw/government/{source_key}.json"
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                logger.info(f"已保存 {len(data)} 条数据到 {output_file}")
                
            except Exception as e:
                logger.error(f"收集 {source_info['name']} 数据时出错: {e}")
        
        return collected_data
    
    def _collect_from_government_source(self, source_info: Dict) -> List[Dict]:
        """从政府数据源收集数据"""
        # 这里是示例实现，实际需要根据具体网站调整
        collected_data = []
        
        # 示例：收集国家卫健委相关文档
        if "nhc.gov.cn" in source_info.get('base_url', ''):
            # 实现具体的爬虫逻辑
            sample_data = {
                "id": "gov_nhc_001",
                "source": "government",
                "source_name": source_info['name'],
                "title": "0-6岁儿童发育行为评估量表使用指南",
                "content": "这是一个示例内容，实际应该从网站抓取...",
                "url": source_info['base_url'],
                "publish_date": "2023-01-01",
                "keywords": ["儿童发育", "评估量表", "行为发展"]
            }
            collected_data.append(sample_data)
        
        return collected_data
    
    def collect_literature_data(self):
        """收集专业文献数据"""
        logger.info("开始收集专业文献数据...")
        
        literature_sources = self.config.get('literature_sources', {})
        collected_data = []
        
        # 处理学术书籍
        academic_books = literature_sources.get('academic_books', [])
        for book in academic_books:
            logger.info(f"处理书籍: {book['title']}")
            
            # 这里需要手动整理或从数字图书馆获取内容
            book_data = self._process_academic_book(book)
            collected_data.extend(book_data)
        
        # 处理百科全书
        encyclopedias = literature_sources.get('encyclopedias', [])
        for encyclopedia in encyclopedias:
            logger.info(f"处理百科全书: {encyclopedia['name']}")
            
            encyclopedia_data = self._process_encyclopedia(encyclopedia)
            collected_data.extend(encyclopedia_data)
        
        # 保存文献数据
        output_file = "data/raw/literature/academic_literature.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(collected_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"已保存 {len(collected_data)} 条文献数据")
        return collected_data
    
    def _process_academic_book(self, book_info: Dict) -> List[Dict]:
        """处理学术书籍数据"""
        # 这里需要手动整理书籍内容或从数字资源获取
        book_data = []
        
        for chapter in book_info.get('chapters', []):
            # 示例数据结构
            chapter_data = {
                "id": f"book_{book_info.get('isbn', 'unknown')}_{len(book_data)}",
                "source": "literature",
                "source_type": "academic_book",
                "book_title": book_info['title'],
                "authors": book_info['authors'],
                "publisher": book_info['publisher'],
                "isbn": book_info.get('isbn'),
                "chapter_title": chapter,
                "content": f"这是《{book_info['title']}》中关于{chapter}的内容...",  # 实际内容需要手动整理
                "keywords": self._extract_keywords_from_chapter(chapter)
            }
            book_data.append(chapter_data)
        
        return book_data
    
    def _process_encyclopedia(self, encyclopedia_info: Dict) -> List[Dict]:
        """处理百科全书数据"""
        encyclopedia_data = []
        
        for entry in encyclopedia_info.get('relevant_entries', []):
            entry_data = {
                "id": f"encyclopedia_{encyclopedia_info['name']}_{len(encyclopedia_data)}",
                "source": "literature",
                "source_type": "encyclopedia",
                "encyclopedia_name": encyclopedia_info['name'],
                "publisher": encyclopedia_info['publisher'],
                "entry_title": entry,
                "content": f"这是《{encyclopedia_info['name']}》中关于{entry}的条目内容...",  # 实际内容需要手动整理
                "keywords": self._extract_keywords_from_entry(entry)
            }
            encyclopedia_data.append(entry_data)
        
        return encyclopedia_data
    
    def _extract_keywords_from_chapter(self, chapter_title: str) -> List[str]:
        """从章节标题提取关键词"""
        # 简单的关键词提取逻辑
        keywords = []
        if "语言发展" in chapter_title:
            keywords.extend(["语言发展", "儿童语言"])
        if "0-3岁" in chapter_title:
            keywords.extend(["婴幼儿", "早期发展"])
        if "3-6岁" in chapter_title:
            keywords.extend(["学龄前", "幼儿期"])
        return keywords
    
    def _extract_keywords_from_entry(self, entry_title: str) -> List[str]:
        """从条目标题提取关键词"""
        # 简单的关键词提取逻辑
        keywords = [entry_title]
        if "语言" in entry_title:
            keywords.append("语言能力")
        if "发展" in entry_title:
            keywords.append("儿童发展")
        return keywords
    
    def run_collection(self):
        """运行完整的数据收集流程"""
        logger.info("开始数据收集流程...")
        
        # 收集政府数据
        gov_data = self.collect_government_data()
        
        # 收集文献数据
        lit_data = self.collect_literature_data()
        
        # 生成收集报告
        self._generate_collection_report(gov_data, lit_data)
        
        logger.info("数据收集完成！")
    
    def _generate_collection_report(self, gov_data: List[Dict], lit_data: List[Dict]):
        """生成数据收集报告"""
        report = {
            "collection_date": time.strftime("%Y-%m-%d %H:%M:%S"),
            "government_data_count": len(gov_data),
            "literature_data_count": len(lit_data),
            "total_data_count": len(gov_data) + len(lit_data),
            "sources_summary": {
                "government_sources": list(self.config.get('government_sources', {}).keys()),
                "literature_sources": len(self.config.get('literature_sources', {}).get('academic_books', [])) + 
                                    len(self.config.get('literature_sources', {}).get('encyclopedias', []))
            }
        }
        
        with open("data/raw/collection_report.json", 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"收集报告已保存，共收集 {report['total_data_count']} 条数据")

def main():
    """主函数"""
    collector = DataCollector()
    collector.run_collection()

if __name__ == "__main__":
    main()
