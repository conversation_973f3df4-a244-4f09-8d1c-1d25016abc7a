#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于完整OCR提取内容生成综合数据集
"""

import json
import re
import uuid
from pathlib import Path
from typing import Dict, List, Tuple
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ComprehensiveDatasetGenerator:
    """综合数据集生成器"""
    
    def __init__(self):
        self.ocr_output_dir = Path("data/raw/literature/ocr_output")
        self.output_dir = Path("data/raw/literature/comprehensive_dataset")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 年龄段映射
        self.age_mapping = {
            '0-3个月': '0-6months',
            '4-6个月': '0-6months', 
            '7-9个月': '6-12months',
            '10-12个月': '6-12months',
            '13-18个月': '1-2years',
            '19-24个月': '1-2years',
            '25-30个月': '2-3years',
            '31-36个月': '2-3years'
        }
    
    def load_full_ocr_content(self) -> Dict:
        """加载完整的OCR内容"""
        logger.info("加载完整的OCR内容...")
        
        pages_file = self.ocr_output_dir / "ocr_all_pages.json"
        if not pages_file.exists():
            logger.error("OCR页面文件不存在")
            return {}
        
        with open(pages_file, 'r', encoding='utf-8') as f:
            pages_data = json.load(f)
        
        logger.info(f"加载了 {len(pages_data)} 页完整内容")
        return pages_data
    
    def extract_theoretical_knowledge(self, pages_data: Dict) -> List[Dict]:
        """提取理论知识内容"""
        logger.info("提取理论知识内容...")
        
        theoretical_data = []
        
        # 定义理论关键词和对应页面范围
        theory_sections = {
            '后天学习决定论': (20, 35),
            '先天遗传决定论': (35, 45), 
            '社会交互作用理论': (45, 55),
            '认知相互作用论': (55, 65),
            '语言发展规律': (65, 75),
            '语言教育理论': (75, 85)
        }
        
        for theory_name, (start_page, end_page) in theory_sections.items():
            theory_content = self._extract_content_from_pages(pages_data, start_page, end_page, theory_name)
            
            if theory_content:
                theoretical_data.append({
                    'id': str(uuid.uuid4()),
                    'source': 'literature',
                    'category': 'language_development',
                    'age_range': '0-3years',
                    'content_type': 'knowledge',
                    'title': theory_name,
                    'content': theory_content,
                    'metadata': {
                        'book_title': '0-3岁婴幼儿语言发展与教育',
                        'authors': ['袁萍', '祝泽舟'],
                        'page_range': f"{start_page}-{end_page}",
                        'keywords': [theory_name, '语言发展理论', '儿童语言获得']
                    }
                })
        
        logger.info(f"提取了 {len(theoretical_data)} 条理论知识")
        return theoretical_data
    
    def extract_development_milestones(self, pages_data: Dict) -> List[Dict]:
        """提取发展里程碑内容"""
        logger.info("提取发展里程碑内容...")
        
        milestone_data = []
        
        # 发展里程碑相关页面（通常在后面章节）
        milestone_pages = range(160, 180)
        
        for page_key in [f"page_{i}" for i in milestone_pages]:
            if page_key in pages_data:
                page_data = pages_data[page_key]
                content = page_data['cleaned_text']
                
                # 查找各个月龄段的评价指标
                age_patterns = [
                    (r'0[一~-]3个月.*?评价指标', '0-3个月'),
                    (r'4[一~-]6个月.*?评价指标', '4-6个月'),
                    (r'7[一~-]9个月.*?评价指标', '7-9个月'),
                    (r'10[一~-]12个月.*?评价指标', '10-12个月'),
                    (r'13[一~-]18个月.*?评价指标', '13-18个月'),
                    (r'19[一~-]24个月.*?评价指标', '19-24个月'),
                    (r'25[一~-]30个月.*?评价指标', '25-30个月'),
                    (r'31[一~-]36个月.*?评价指标', '31-36个月')
                ]
                
                for pattern, age_desc in age_patterns:
                    if re.search(pattern, content):
                        milestone_content = self._extract_milestone_table(content, age_desc)
                        
                        if milestone_content and len(milestone_content) > 100:
                            milestone_data.append({
                                'id': str(uuid.uuid4()),
                                'source': 'literature',
                                'category': 'language_development',
                                'age_range': self.age_mapping.get(age_desc, 'unknown'),
                                'content_type': 'development_milestones',
                                'title': f'{age_desc}婴幼儿语言发展评价指标',
                                'content': milestone_content,
                                'metadata': {
                                    'book_title': '0-3岁婴幼儿语言发展与教育',
                                    'authors': ['袁萍', '祝泽舟'],
                                    'page_number': page_data['page_number'],
                                    'keywords': ['发展评价', age_desc, '语言里程碑']
                                }
                            })
        
        logger.info(f"提取了 {len(milestone_data)} 条发展里程碑")
        return milestone_data
    
    def extract_practical_guidance(self, pages_data: Dict) -> List[Dict]:
        """提取实践指导内容"""
        logger.info("提取实践指导内容...")
        
        guidance_data = []
        
        # 实践指导相关页面（通常在中间章节）
        guidance_pages = range(85, 160)
        
        guidance_keywords = [
            '语言教育方法', '语言训练', '语言游戏', '亲子交流',
            '语言环境', '语言刺激', '语言活动', '语言指导'
        ]
        
        for page_key in [f"page_{i}" for i in guidance_pages]:
            if page_key in pages_data:
                page_data = pages_data[page_key]
                content = page_data['cleaned_text']
                
                for keyword in guidance_keywords:
                    if keyword in content:
                        guidance_content = self._extract_guidance_section(content, keyword)
                        
                        if guidance_content and len(guidance_content) > 150:
                            # 根据内容判断适用年龄段
                            age_range = self._determine_age_range_from_content(content)
                            
                            guidance_data.append({
                                'id': str(uuid.uuid4()),
                                'source': 'literature',
                                'category': 'language_development',
                                'age_range': age_range,
                                'content_type': 'guidance_methods',
                                'title': f'{keyword}指导方法',
                                'content': guidance_content,
                                'metadata': {
                                    'book_title': '0-3岁婴幼儿语言发展与教育',
                                    'authors': ['袁萍', '祝泽舟'],
                                    'page_number': page_data['page_number'],
                                    'keywords': [keyword, '实践指导', '语言教育']
                                }
                            })
                            break  # 每页只提取一个指导方法，避免重复
        
        logger.info(f"提取了 {len(guidance_data)} 条实践指导")
        return guidance_data
    
    def generate_comprehensive_qa(self, theoretical_data: List[Dict], milestone_data: List[Dict], guidance_data: List[Dict]) -> List[Dict]:
        """基于提取内容生成综合QA问答对"""
        logger.info("生成综合QA问答对...")
        
        qa_data = []
        
        # 基于理论知识生成QA
        for theory in theoretical_data:
            title = theory['title']
            content = theory['content']
            
            # 理论解释类问题
            qa_data.append({
                'id': str(uuid.uuid4()),
                'source': 'qa',
                'category': 'language_development',
                'age_range': theory['age_range'],
                'content_type': 'qa_pair',
                'title': f'什么是{title}？',
                'content': f'{title}是语言发展的重要理论。{content[:300]}...\n\n这一理论为我们理解婴幼儿语言发展提供了重要的理论基础。',
                'metadata': {
                    'question_type': 'theory_explanation',
                    'difficulty': 'advanced',
                    'source_book': '0-3岁婴幼儿语言发展与教育'
                }
            })
        
        # 基于发展里程碑生成QA
        for milestone in milestone_data:
            title = milestone['title']
            content = milestone['content']
            age_range = milestone['age_range']
            
            # 提取年龄描述
            age_desc = title.split('婴幼儿')[0] if '婴幼儿' in title else age_range
            
            qa_data.append({
                'id': str(uuid.uuid4()),
                'source': 'qa',
                'category': 'language_development',
                'age_range': age_range,
                'content_type': 'qa_pair',
                'title': f'{age_desc}的孩子语言发展有什么特点？',
                'content': f'{age_desc}孩子的语言发展主要表现在以下几个方面：\n\n{content[:500]}...\n\n家长可以根据这些指标来观察和评估孩子的语言发展情况。',
                'metadata': {
                    'question_type': 'milestone',
                    'difficulty': 'basic',
                    'source_book': '0-3岁婴幼儿语言发展与教育'
                }
            })
        
        # 基于实践指导生成QA
        for guidance in guidance_data:
            title = guidance['title']
            content = guidance['content']
            age_range = guidance['age_range']
            
            method_name = title.replace('指导方法', '')
            
            qa_data.append({
                'id': str(uuid.uuid4()),
                'source': 'qa',
                'category': 'language_development',
                'age_range': age_range,
                'content_type': 'qa_pair',
                'title': f'如何进行{method_name}？',
                'content': f'{method_name}是促进婴幼儿语言发展的重要方法：\n\n{content[:400]}...\n\n通过这些方法，可以有效促进孩子的语言发展。',
                'metadata': {
                    'question_type': 'guidance',
                    'difficulty': 'intermediate',
                    'source_book': '0-3岁婴幼儿语言发展与教育'
                }
            })
        
        logger.info(f"生成了 {len(qa_data)} 个综合QA问答对")
        return qa_data
    
    def _extract_content_from_pages(self, pages_data: Dict, start_page: int, end_page: int, keyword: str) -> str:
        """从指定页面范围提取内容"""
        combined_content = ""
        
        for page_num in range(start_page, end_page + 1):
            page_key = f"page_{page_num}"
            if page_key in pages_data:
                content = pages_data[page_key]['cleaned_text']
                if keyword in content:
                    combined_content += content + "\n\n"
        
        # 清理和截取内容
        if combined_content:
            combined_content = combined_content.strip()
            if len(combined_content) > 1500:
                combined_content = combined_content[:1500] + "..."
        
        return combined_content
    
    def _extract_milestone_table(self, content: str, age_desc: str) -> str:
        """提取里程碑表格内容"""
        # 查找年龄段的位置
        start_pos = content.find(age_desc)
        if start_pos == -1:
            return ""
        
        # 提取相关内容
        milestone_content = content[start_pos:]
        
        # 查找下一个年龄段或章节，作为结束位置
        next_sections = ['个月婴幼儿', '岁婴幼儿', '第二节', '第三节', '表9-']
        end_pos = len(milestone_content)
        
        for section in next_sections:
            pos = milestone_content.find(section, 50)  # 从50字符后开始查找
            if pos != -1 and pos < end_pos:
                end_pos = pos
        
        milestone_content = milestone_content[:end_pos]
        
        # 限制长度
        if len(milestone_content) > 1000:
            milestone_content = milestone_content[:1000] + "..."
        
        return milestone_content.strip()
    
    def _extract_guidance_section(self, content: str, keyword: str) -> str:
        """提取指导方法章节内容"""
        # 查找关键词的位置
        start_pos = content.find(keyword)
        if start_pos == -1:
            return ""
        
        # 提取从关键词开始的内容
        guidance_content = content[start_pos:]
        
        # 限制长度
        if len(guidance_content) > 800:
            guidance_content = guidance_content[:800] + "..."
        
        return guidance_content.strip()
    
    def _determine_age_range_from_content(self, content: str) -> str:
        """根据内容判断适用年龄段"""
        age_indicators = {
            '0-6months': ['0-3个月', '4-6个月', '新生儿', '婴儿期早期'],
            '6-12months': ['7-9个月', '10-12个月', '婴儿期晚期', '咿呀学语'],
            '1-2years': ['13-18个月', '19-24个月', '1-2岁', '幼儿期早期'],
            '2-3years': ['25-30个月', '31-36个月', '2-3岁', '幼儿期']
        }
        
        for age_range, indicators in age_indicators.items():
            for indicator in indicators:
                if indicator in content:
                    return age_range
        
        return '0-3years'  # 默认返回
    
    def generate_complete_dataset(self) -> List[Dict]:
        """生成完整的综合数据集"""
        logger.info("开始生成基于完整书籍内容的综合数据集...")
        
        # 加载完整OCR内容
        pages_data = self.load_full_ocr_content()
        if not pages_data:
            return []
        
        # 提取各类内容
        theoretical_data = self.extract_theoretical_knowledge(pages_data)
        milestone_data = self.extract_development_milestones(pages_data)
        guidance_data = self.extract_practical_guidance(pages_data)
        
        # 生成综合QA问答对
        qa_data = self.generate_comprehensive_qa(theoretical_data, milestone_data, guidance_data)
        
        # 合并所有数据
        complete_dataset = theoretical_data + milestone_data + guidance_data + qa_data
        
        logger.info(f"完整综合数据集包含 {len(complete_dataset)} 条记录")
        return complete_dataset
    
    def save_dataset(self, dataset: List[Dict]):
        """保存数据集"""
        logger.info("保存综合数据集...")
        
        # 保存为JSON格式
        output_file = self.output_dir / "comprehensive_dataset.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(dataset, f, ensure_ascii=False, indent=2)
        
        # 生成详细统计报告
        report = {
            'total_records': len(dataset),
            'by_content_type': {},
            'by_age_range': {},
            'by_source': {},
            'by_difficulty': {}
        }
        
        for item in dataset:
            # 按内容类型统计
            content_type = item.get('content_type', 'unknown')
            report['by_content_type'][content_type] = report['by_content_type'].get(content_type, 0) + 1
            
            # 按年龄段统计
            age_range = item.get('age_range', 'unknown')
            report['by_age_range'][age_range] = report['by_age_range'].get(age_range, 0) + 1
            
            # 按来源统计
            source = item.get('source', 'unknown')
            report['by_source'][source] = report['by_source'].get(source, 0) + 1
            
            # 按难度统计
            difficulty = item.get('metadata', {}).get('difficulty', 'unknown')
            if difficulty != 'unknown':
                report['by_difficulty'][difficulty] = report['by_difficulty'].get(difficulty, 0) + 1
        
        # 保存报告
        report_file = self.output_dir / "comprehensive_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"综合数据集已保存到 {output_file}")
        logger.info(f"统计报告已保存到 {report_file}")
        
        return report

def main():
    """主函数"""
    generator = ComprehensiveDatasetGenerator()
    
    # 生成综合数据集
    dataset = generator.generate_complete_dataset()
    
    if dataset:
        # 保存数据集
        report = generator.save_dataset(dataset)
        
        # 显示结果
        print(f"\n=== 基于完整书籍内容的综合数据集生成完成 ===")
        print(f"总记录数: {report['total_records']}")
        
        print(f"\n按内容类型分布:")
        for content_type, count in report['by_content_type'].items():
            print(f"  {content_type}: {count}")
        
        print(f"\n按年龄段分布:")
        for age_range, count in report['by_age_range'].items():
            print(f"  {age_range}: {count}")
        
        print(f"\n按来源分布:")
        for source, count in report['by_source'].items():
            print(f"  {source}: {count}")
        
        if report['by_difficulty']:
            print(f"\n按难度分布:")
            for difficulty, count in report['by_difficulty'].items():
                print(f"  {difficulty}: {count}")
        
        # 显示样本内容
        print(f"\n=== 数据样本 ===")
        for i, item in enumerate(dataset[:5]):
            print(f"\n{i+1}. {item['title']}")
            print(f"   类型: {item['content_type']} | 年龄段: {item['age_range']} | 来源: {item['source']}")
            print(f"   内容: {item['content'][:100]}...")
    
    else:
        logger.error("综合数据集生成失败")

if __name__ == "__main__":
    main()
