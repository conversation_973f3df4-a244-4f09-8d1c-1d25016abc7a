#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF内容提取脚本 - 提取《0-3岁婴幼儿语言发展与教育》书籍内容
"""

import pdfplumber
import PyPDF2
import json
import re
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PDFContentExtractor:
    """PDF内容提取器"""
    
    def __init__(self, pdf_path):
        self.pdf_path = pdf_path
        self.content = []
        self.toc = []  # 目录
        
    def extract_with_pdfplumber(self):
        """使用pdfplumber提取内容"""
        logger.info("使用pdfplumber提取PDF内容...")
        
        try:
            with pdfplumber.open(self.pdf_path) as pdf:
                logger.info(f"PDF总页数: {len(pdf.pages)}")
                
                all_text = ""
                for i, page in enumerate(pdf.pages):
                    try:
                        text = page.extract_text()
                        if text:
                            all_text += f"\n--- 第{i+1}页 ---\n"
                            all_text += text
                            
                        # 每10页输出一次进度
                        if (i + 1) % 10 == 0:
                            logger.info(f"已处理 {i+1}/{len(pdf.pages)} 页")
                            
                    except Exception as e:
                        logger.warning(f"处理第{i+1}页时出错: {e}")
                        continue
                
                return all_text
                
        except Exception as e:
            logger.error(f"pdfplumber提取失败: {e}")
            return None
    
    def extract_with_pypdf2(self):
        """使用PyPDF2提取内容"""
        logger.info("使用PyPDF2提取PDF内容...")
        
        try:
            with open(self.pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                logger.info(f"PDF总页数: {len(pdf_reader.pages)}")
                
                all_text = ""
                for i, page in enumerate(pdf_reader.pages):
                    try:
                        text = page.extract_text()
                        if text:
                            all_text += f"\n--- 第{i+1}页 ---\n"
                            all_text += text
                            
                        # 每10页输出一次进度
                        if (i + 1) % 10 == 0:
                            logger.info(f"已处理 {i+1}/{len(pdf_reader.pages)} 页")
                            
                    except Exception as e:
                        logger.warning(f"处理第{i+1}页时出错: {e}")
                        continue
                
                return all_text
                
        except Exception as e:
            logger.error(f"PyPDF2提取失败: {e}")
            return None
    
    def clean_text(self, text):
        """清理提取的文本"""
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除页眉页脚等重复内容
        text = re.sub(r'第\d+页', '', text)
        text = re.sub(r'--- 第\d+页 ---', '\n\n', text)
        
        # 移除特殊字符
        text = re.sub(r'[^\u4e00-\u9fff\w\s，。！？；：""''（）【】《》\n]', '', text)
        
        return text.strip()
    
    def extract_chapters(self, text):
        """提取章节内容"""
        logger.info("提取章节内容...")
        
        chapters = []
        
        # 常见的章节标题模式
        chapter_patterns = [
            r'第[一二三四五六七八九十\d]+章\s*[^\n]+',
            r'第[一二三四五六七八九十\d]+节\s*[^\n]+',
            r'\d+\.\d+\s*[^\n]+',
            r'\d+\s*[^\n]{5,30}',
        ]
        
        # 查找所有可能的章节标题
        potential_titles = []
        for pattern in chapter_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                potential_titles.append({
                    'title': match.group().strip(),
                    'start': match.start(),
                    'end': match.end()
                })
        
        # 按位置排序
        potential_titles.sort(key=lambda x: x['start'])
        
        # 提取章节内容
        for i, title_info in enumerate(potential_titles):
            title = title_info['title']
            start_pos = title_info['end']
            
            # 确定章节结束位置
            if i + 1 < len(potential_titles):
                end_pos = potential_titles[i + 1]['start']
            else:
                end_pos = len(text)
            
            content = text[start_pos:end_pos].strip()
            
            # 过滤太短的内容
            if len(content) > 100:
                chapters.append({
                    'title': title,
                    'content': content[:2000],  # 限制长度
                    'full_content': content
                })
        
        logger.info(f"提取到 {len(chapters)} 个章节")
        return chapters
    
    def extract_key_concepts(self, text):
        """提取关键概念"""
        logger.info("提取关键概念...")
        
        # 语言发展相关的关键词
        key_terms = [
            '语言发展', '语言发育', '言语发展', '语音发展',
            '词汇发展', '语法发展', '语义发展', '语用发展',
            '咿呀学语', '单词期', '双词期', '电报句',
            '语言理解', '语言表达', '语言交流',
            '发展里程碑', '关键期', '敏感期',
            '语言障碍', '语言迟缓', '构音障碍',
            '亲子互动', '语言环境', '语言刺激'
        ]
        
        concepts = {}
        for term in key_terms:
            # 查找包含关键词的句子
            pattern = f'[^。！？]*{term}[^。！？]*[。！？]'
            matches = re.findall(pattern, text)
            if matches:
                concepts[term] = matches[:3]  # 最多保留3个例句
        
        return concepts
    
    def save_extracted_content(self, text, chapters, concepts):
        """保存提取的内容"""
        logger.info("保存提取的内容...")
        
        # 保存原始文本
        with open('data/raw/literature/pdf_full_text.txt', 'w', encoding='utf-8') as f:
            f.write(text)
        
        # 保存章节内容
        with open('data/raw/literature/pdf_chapters.json', 'w', encoding='utf-8') as f:
            json.dump(chapters, f, ensure_ascii=False, indent=2)
        
        # 保存关键概念
        with open('data/raw/literature/pdf_concepts.json', 'w', encoding='utf-8') as f:
            json.dump(concepts, f, ensure_ascii=False, indent=2)
        
        logger.info("内容保存完成")
    
    def extract_all(self):
        """执行完整的提取流程"""
        logger.info(f"开始提取PDF内容: {self.pdf_path}")
        
        # 尝试两种方法提取文本
        text = self.extract_with_pdfplumber()
        if not text or len(text) < 1000:
            logger.info("pdfplumber提取效果不佳，尝试PyPDF2...")
            text = self.extract_with_pypdf2()
        
        if not text:
            logger.error("无法提取PDF内容")
            return None
        
        logger.info(f"提取的文本长度: {len(text)} 字符")
        
        # 清理文本
        cleaned_text = self.clean_text(text)
        logger.info(f"清理后文本长度: {len(cleaned_text)} 字符")
        
        # 提取章节
        chapters = self.extract_chapters(cleaned_text)
        
        # 提取关键概念
        concepts = self.extract_key_concepts(cleaned_text)
        
        # 保存结果
        self.save_extracted_content(cleaned_text, chapters, concepts)
        
        return {
            'text': cleaned_text,
            'chapters': chapters,
            'concepts': concepts
        }

def main():
    """主函数"""
    pdf_path = "0-3岁婴幼儿语言发展与教育 (袁萍，祝泽舟主编；张敏，乔芳玲，张建国副主编, 袁萍, 祝泽舟主编, 袁萍, 祝泽舟) (Z-Library)-已压缩.pdf"
    
    if not Path(pdf_path).exists():
        logger.error(f"PDF文件不存在: {pdf_path}")
        return
    
    extractor = PDFContentExtractor(pdf_path)
    result = extractor.extract_all()
    
    if result:
        logger.info("PDF内容提取完成！")
        logger.info(f"提取到 {len(result['chapters'])} 个章节")
        logger.info(f"提取到 {len(result['concepts'])} 个关键概念")
        
        # 显示前几个章节标题
        print("\n=== 提取的章节标题 ===")
        for i, chapter in enumerate(result['chapters'][:10]):
            print(f"{i+1}. {chapter['title']}")
        
        # 显示关键概念
        print("\n=== 关键概念示例 ===")
        for term, examples in list(result['concepts'].items())[:5]:
            print(f"{term}: {len(examples)} 个相关句子")
    else:
        logger.error("PDF内容提取失败")

if __name__ == "__main__":
    main()
