#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
婴幼儿语言发展指导数据验证脚本
"""

import json
import yaml
import re
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import logging
from jsonschema import validate, ValidationError
import pandas as pd

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DataValidator:
    """数据验证器"""
    
    def __init__(self, config_path: str = "config/processing_config.yaml"):
        """初始化数据验证器"""
        self.config = self._load_config(config_path)
        self.validation_config = self.config.get('validation', {})
        
        # 定义数据模式
        self.data_schema = {
            "type": "object",
            "required": ["id", "source", "category", "age_range", "content"],
            "properties": {
                "id": {"type": "string"},
                "source": {
                    "type": "string",
                    "enum": ["government", "literature", "qa"]
                },
                "category": {"type": "string"},
                "age_range": {
                    "type": "string",
                    "enum": ["0-6months", "6-12months", "1-2years", "2-3years", "3-6years", "unknown"]
                },
                "content_type": {"type": "string"},
                "title": {"type": "string"},
                "content": {"type": "string", "minLength": 1},
                "metadata": {"type": "object"}
            }
        }
        
        # 创建输出目录
        Path("data/final").mkdir(parents=True, exist_ok=True)
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def load_processed_data(self) -> List[Dict]:
        """加载处理后的数据"""
        data_file = Path("data/processed/processed_dataset.json")
        
        if not data_file.exists():
            logger.error("处理后的数据文件不存在，请先运行预处理脚本")
            return []
        
        with open(data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        logger.info(f"加载了 {len(data)} 条处理后的数据")
        return data
    
    def validate_schema(self, data: List[Dict]) -> Tuple[List[Dict], List[Dict]]:
        """验证数据模式"""
        logger.info("验证数据模式...")
        
        valid_data = []
        invalid_data = []
        
        for i, item in enumerate(data):
            try:
                validate(instance=item, schema=self.data_schema)
                valid_data.append(item)
            except ValidationError as e:
                logger.warning(f"数据项 {i} 模式验证失败: {e.message}")
                invalid_data.append({
                    "index": i,
                    "data": item,
                    "error": e.message
                })
        
        logger.info(f"模式验证完成，有效数据: {len(valid_data)}，无效数据: {len(invalid_data)}")
        return valid_data, invalid_data
    
    def validate_content_quality(self, data: List[Dict]) -> List[Dict]:
        """验证内容质量"""
        logger.info("验证内容质量...")
        
        quality_checks = self.validation_config.get('quality_checks', {})
        high_quality_data = []
        
        for item in data:
            content = item.get('content', '')
            
            # 检查内容长度
            length_config = quality_checks.get('content_length', {})
            min_length = length_config.get('min', 50)
            max_length = length_config.get('max', 5000)
            
            if not (min_length <= len(content) <= max_length):
                logger.debug(f"内容长度不符合要求: {item.get('id')}")
                continue
            
            # 检查内容质量
            if self._check_content_quality(content):
                high_quality_data.append(item)
            else:
                logger.debug(f"内容质量不符合要求: {item.get('id')}")
        
        logger.info(f"质量验证完成，高质量数据: {len(high_quality_data)}")
        return high_quality_data
    
    def _check_content_quality(self, content: str) -> bool:
        """检查内容质量"""
        # 检查是否包含足够的中文字符
        chinese_chars = re.findall(r'[\u4e00-\u9fff]', content)
        if len(chinese_chars) < 20:  # 至少包含20个中文字符
            return False
        
        # 检查是否包含过多的特殊字符
        special_chars = re.findall(r'[^\w\s\u4e00-\u9fff，。！？；：""''（）【】《》]', content)
        if len(special_chars) > len(content) * 0.1:  # 特殊字符不超过10%
            return False
        
        # 检查是否包含明显的错误模式
        error_patterns = [
            r'测试',
            r'示例',
            r'TODO',
            r'待完善',
            r'占位符'
        ]
        
        for pattern in error_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                return False
        
        return True
    
    def validate_age_consistency(self, data: List[Dict]) -> List[Dict]:
        """验证年龄一致性"""
        logger.info("验证年龄一致性...")
        
        consistent_data = []
        
        for item in data:
            age_range = item.get('age_range', '')
            content = item.get('content', '')
            title = item.get('title', '')
            
            # 检查年龄范围与内容的一致性
            if self._check_age_consistency(age_range, content + ' ' + title):
                consistent_data.append(item)
            else:
                logger.debug(f"年龄一致性检查失败: {item.get('id')}")
        
        logger.info(f"年龄一致性验证完成，一致数据: {len(consistent_data)}")
        return consistent_data
    
    def _check_age_consistency(self, age_range: str, text: str) -> bool:
        """检查年龄一致性"""
        age_keywords = {
            "0-6months": ["新生儿", "0-6个月", "婴儿期早期", "出生", "月龄"],
            "6-12months": ["6-12个月", "婴儿期晚期", "咿呀学语", "爬行"],
            "1-2years": ["1-2岁", "幼儿期早期", "词汇爆发", "走路"],
            "2-3years": ["2-3岁", "幼儿期", "语法发展", "句子"],
            "3-6years": ["3-6岁", "学龄前", "语言完善", "故事"]
        }
        
        if age_range == "unknown":
            return True  # 未知年龄范围暂时通过
        
        keywords = age_keywords.get(age_range, [])
        
        # 如果内容中包含对应年龄段的关键词，认为一致
        for keyword in keywords:
            if keyword in text:
                return True
        
        # 如果没有明确的年龄冲突，也认为一致
        conflicting_keywords = []
        for other_age, other_keywords in age_keywords.items():
            if other_age != age_range:
                conflicting_keywords.extend(other_keywords)
        
        for keyword in conflicting_keywords:
            if keyword in text:
                return False  # 发现冲突的年龄关键词
        
        return True  # 没有冲突，认为一致
    
    def validate_professional_terms(self, data: List[Dict]) -> List[Dict]:
        """验证专业术语一致性"""
        logger.info("验证专业术语一致性...")
        
        # 定义专业术语词典
        professional_terms = {
            "语言发展": ["语言发育", "言语发展", "语言能力发展"],
            "认知发展": ["认知发育", "智力发展", "认知能力发展"],
            "里程碑": ["发展里程碑", "发育里程碑", "发展指标"],
            "语言障碍": ["言语障碍", "语言发育迟缓", "语言发展障碍"]
        }
        
        validated_data = []
        
        for item in data:
            content = item.get('content', '')
            
            # 标准化专业术语
            standardized_content = self._standardize_terms(content, professional_terms)
            
            # 更新内容
            item['content'] = standardized_content
            validated_data.append(item)
        
        logger.info(f"专业术语验证完成，处理了 {len(validated_data)} 条数据")
        return validated_data
    
    def _standardize_terms(self, content: str, term_dict: Dict[str, List[str]]) -> str:
        """标准化术语"""
        for standard_term, variants in term_dict.items():
            for variant in variants:
                content = content.replace(variant, standard_term)
        
        return content
    
    def generate_final_dataset(self, data: List[Dict]) -> List[Dict]:
        """生成最终数据集"""
        logger.info("生成最终数据集...")
        
        # 按年龄段分组
        age_groups = {}
        for item in data:
            age_range = item.get('age_range', 'unknown')
            if age_range not in age_groups:
                age_groups[age_range] = []
            age_groups[age_range].append(item)
        
        # 确保每个年龄段都有足够的数据
        final_data = []
        min_samples_per_age = 2  # 每个年龄段至少2条数据（降低要求用于演示）
        
        for age_range, items in age_groups.items():
            if len(items) >= min_samples_per_age or age_range == 'unknown':
                final_data.extend(items)
                logger.info(f"{age_range}: {len(items)} 条数据")
            else:
                logger.warning(f"{age_range} 数据不足 ({len(items)} < {min_samples_per_age})，已排除")
        
        # 重新分配ID
        for i, item in enumerate(final_data):
            item['id'] = f"dataset_{i:06d}"
        
        logger.info(f"最终数据集包含 {len(final_data)} 条数据")
        return final_data
    
    def validate_all(self) -> List[Dict]:
        """执行完整的验证流程"""
        logger.info("开始数据验证流程...")
        
        # 1. 加载处理后的数据
        data = self.load_processed_data()
        if not data:
            return []
        
        # 2. 模式验证
        valid_data, invalid_data = self.validate_schema(data)
        
        # 保存无效数据报告
        if invalid_data:
            with open("data/final/invalid_data_report.json", 'w', encoding='utf-8') as f:
                json.dump(invalid_data, f, ensure_ascii=False, indent=2)
        
        # 3. 内容质量验证
        quality_data = self.validate_content_quality(valid_data)
        
        # 4. 年龄一致性验证
        consistent_data = self.validate_age_consistency(quality_data)
        
        # 5. 专业术语验证
        professional_data = self.validate_professional_terms(consistent_data)
        
        # 6. 生成最终数据集
        final_data = self.generate_final_dataset(professional_data)
        
        return final_data
    
    def save_final_dataset(self, data: List[Dict]):
        """保存最终数据集"""
        # 保存为JSON格式
        json_file = "data/final/infant_language_development_dataset.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        # 保存为JSONL格式（用于训练）
        jsonl_file = "data/final/infant_language_development_dataset.jsonl"
        with open(jsonl_file, 'w', encoding='utf-8') as f:
            for item in data:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
        
        logger.info(f"最终数据集已保存:")
        logger.info(f"  JSON格式: {json_file}")
        logger.info(f"  JSONL格式: {jsonl_file}")
        
        # 生成验证报告
        self._generate_validation_report(data)
    
    def _generate_validation_report(self, data: List[Dict]):
        """生成验证报告"""
        report = {
            "dataset_info": {
                "total_records": len(data),
                "creation_date": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S"),
                "version": "1.0"
            },
            "distribution": {
                "by_source": {},
                "by_age_range": {},
                "by_content_type": {}
            },
            "quality_metrics": {
                "avg_content_length": 0,
                "min_content_length": 0,
                "max_content_length": 0
            }
        }
        
        content_lengths = []
        
        for item in data:
            # 按来源分布
            source = item.get('source', 'unknown')
            report['distribution']['by_source'][source] = \
                report['distribution']['by_source'].get(source, 0) + 1
            
            # 按年龄段分布
            age_range = item.get('age_range', 'unknown')
            report['distribution']['by_age_range'][age_range] = \
                report['distribution']['by_age_range'].get(age_range, 0) + 1
            
            # 按内容类型分布
            content_type = item.get('content_type', 'unknown')
            report['distribution']['by_content_type'][content_type] = \
                report['distribution']['by_content_type'].get(content_type, 0) + 1
            
            # 内容长度统计
            content_length = len(item.get('content', ''))
            content_lengths.append(content_length)
        
        if content_lengths:
            report['quality_metrics'] = {
                "avg_content_length": sum(content_lengths) / len(content_lengths),
                "min_content_length": min(content_lengths),
                "max_content_length": max(content_lengths)
            }
        
        with open("data/final/validation_report.json", 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info("验证报告已生成")

def main():
    """主函数"""
    validator = DataValidator()
    final_data = validator.validate_all()
    if final_data:
        validator.save_final_dataset(final_data)
    else:
        logger.error("验证失败，没有生成最终数据集")

if __name__ == "__main__":
    main()
