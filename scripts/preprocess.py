#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
婴幼儿语言发展指导数据预处理脚本
"""

import json
import yaml
import re
import os
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import logging
import jieba
import pandas as pd
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DataPreprocessor:
    """数据预处理器"""
    
    def __init__(self, config_path: str = "config/processing_config.yaml"):
        """初始化数据预处理器"""
        self.config = self._load_config(config_path)
        self.preprocessing_config = self.config.get('preprocessing', {})
        self.standardization_config = self.config.get('standardization', {})
        
        # 创建输出目录
        Path("data/processed").mkdir(parents=True, exist_ok=True)
        
        # 初始化分词器
        jieba.initialize()
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def load_raw_data(self) -> List[Dict]:
        """加载原始数据"""
        logger.info("加载原始数据...")

        all_data = []

        # 优先加载整合后的数据集
        integrated_data_file = Path("data/raw/integrated/integrated_dataset.json")
        if integrated_data_file.exists():
            with open(integrated_data_file, 'r', encoding='utf-8') as f:
                integrated_data = json.load(f)
                all_data.extend(integrated_data)
                logger.info(f"加载了整合数据集: {len(integrated_data)} 条")
        else:
            # 如果没有整合数据集，加载原始数据
            # 加载政府数据
            gov_data_dir = Path("data/raw/government")
            if gov_data_dir.exists():
                for file_path in gov_data_dir.glob("*.json"):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        if isinstance(data, list):
                            all_data.extend(data)
                        else:
                            all_data.append(data)

            # 加载文献数据
            lit_data_file = Path("data/raw/literature/academic_literature.json")
            if lit_data_file.exists():
                with open(lit_data_file, 'r', encoding='utf-8') as f:
                    lit_data = json.load(f)
                    all_data.extend(lit_data)

            # 加载QA数据
            qa_data_file = Path("data/raw/qa_pairs/generated_qa.json")
            if qa_data_file.exists():
                with open(qa_data_file, 'r', encoding='utf-8') as f:
                    qa_data = json.load(f)
                    all_data.extend(qa_data)

        logger.info(f"共加载 {len(all_data)} 条原始数据")
        return all_data
    
    def clean_text(self, text: str) -> str:
        """清洗文本"""
        if not text:
            return ""
        
        cleaning_config = self.preprocessing_config.get('text_cleaning', {})
        
        # 移除HTML标签
        if cleaning_config.get('remove_html_tags', True):
            text = re.sub(r'<[^>]+>', '', text)
        
        # 标准化空白字符
        if cleaning_config.get('normalize_whitespace', True):
            text = re.sub(r'\s+', ' ', text)
            text = text.strip()
        
        # 移除空行
        if cleaning_config.get('remove_empty_lines', True):
            lines = text.split('\n')
            lines = [line.strip() for line in lines if line.strip()]
            text = '\n'.join(lines)
        
        return text
    
    def standardize_age_range(self, text: str) -> str:
        """标准化年龄范围"""
        age_groups = self.standardization_config.get('age_groups', [])
        
        for age_group in age_groups:
            keywords = age_group.get('keywords', [])
            for keyword in keywords:
                if keyword in text:
                    return age_group['name']
        
        # 默认返回
        return "unknown"
    
    def categorize_content(self, text: str, metadata: Dict = None) -> str:
        """内容分类"""
        categories = self.standardization_config.get('content_categories', [])
        
        text_lower = text.lower()
        
        for category in categories:
            keywords = category.get('keywords', [])
            for keyword in keywords:
                if keyword in text_lower:
                    return category['name']
        
        # 根据元数据判断
        if metadata:
            if 'question' in metadata or 'answer' in metadata:
                return 'qa_pair'
            elif 'government' in metadata.get('source', ''):
                return 'guideline'
            elif 'literature' in metadata.get('source', ''):
                return 'knowledge'
        
        return 'general'
    
    def remove_duplicates(self, data: List[Dict]) -> List[Dict]:
        """去除重复数据"""
        logger.info("去除重复数据...")
        
        # 使用TF-IDF计算文本相似度
        texts = []
        for item in data:
            content = item.get('content', '') or item.get('answer', '') or item.get('title', '')
            texts.append(content)
        
        if not texts:
            return data
        
        # 计算TF-IDF向量
        vectorizer = TfidfVectorizer(max_features=1000, stop_words=None)
        try:
            tfidf_matrix = vectorizer.fit_transform(texts)
            
            # 计算相似度矩阵
            similarity_matrix = cosine_similarity(tfidf_matrix)
            
            # 找出重复项
            threshold = self.preprocessing_config.get('content_filtering', {}).get('similarity_threshold', 0.85)
            duplicates = set()
            
            for i in range(len(similarity_matrix)):
                for j in range(i + 1, len(similarity_matrix)):
                    if similarity_matrix[i][j] > threshold:
                        duplicates.add(j)  # 保留第一个，删除后面的
            
            # 过滤重复项
            filtered_data = [data[i] for i in range(len(data)) if i not in duplicates]
            
            logger.info(f"去除了 {len(duplicates)} 条重复数据，剩余 {len(filtered_data)} 条")
            return filtered_data
            
        except Exception as e:
            logger.warning(f"去重过程中出现错误: {e}，返回原始数据")
            return data
    
    def filter_by_length(self, data: List[Dict]) -> List[Dict]:
        """根据长度过滤数据"""
        logger.info("根据长度过滤数据...")
        
        filtering_config = self.preprocessing_config.get('content_filtering', {})
        min_length = filtering_config.get('min_length', 50)
        max_length = filtering_config.get('max_length', 5000)
        
        filtered_data = []
        for item in data:
            content = item.get('content', '') or item.get('answer', '') or item.get('title', '')
            content_length = len(content)
            
            if min_length <= content_length <= max_length:
                filtered_data.append(item)
        
        logger.info(f"长度过滤后剩余 {len(filtered_data)} 条数据")
        return filtered_data
    
    def standardize_data_format(self, data: List[Dict]) -> List[Dict]:
        """标准化数据格式"""
        logger.info("标准化数据格式...")
        
        standardized_data = []
        
        for item in data:
            # 创建标准化的数据结构
            standardized_item = {
                "id": item.get('id', f"item_{len(standardized_data)}"),
                "source": item.get('source', 'unknown'),
                "category": "language_development",
                "age_range": item.get('age_range', self.standardize_age_range(str(item))),
                "content_type": item.get('content_type', self.categorize_content(str(item), item)),
                "title": item.get('title', item.get('question', '')),
                "content": self.clean_text(item.get('content', item.get('answer', ''))),
                "metadata": {
                    "original_source": item.get('source_name', item.get('book_title', '')),
                    "publish_date": item.get('publish_date', ''),
                    "source_url": item.get('url', ''),
                    "keywords": item.get('keywords', []),
                    "authors": item.get('authors', []),
                    "question_type": item.get('metadata', {}).get('question_type', ''),
                    "difficulty": item.get('metadata', {}).get('difficulty', ''),
                    "focus_areas": item.get('metadata', {}).get('focus_areas', [])
                }
            }
            
            # 只保留有内容的项目
            if standardized_item['content'].strip():
                standardized_data.append(standardized_item)
        
        logger.info(f"标准化后共有 {len(standardized_data)} 条数据")
        return standardized_data
    
    def process_all_data(self) -> List[Dict]:
        """处理所有数据"""
        logger.info("开始数据预处理流程...")
        
        # 1. 加载原始数据
        raw_data = self.load_raw_data()
        
        # 2. 根据长度过滤
        filtered_data = self.filter_by_length(raw_data)
        
        # 3. 去除重复
        dedup_data = self.remove_duplicates(filtered_data)
        
        # 4. 标准化格式
        standardized_data = self.standardize_data_format(dedup_data)
        
        return standardized_data
    
    def save_processed_data(self, data: List[Dict]):
        """保存处理后的数据"""
        output_file = "data/processed/processed_dataset.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"处理后的数据已保存到 {output_file}")
        
        # 生成处理报告
        self._generate_processing_report(data)
    
    def _generate_processing_report(self, data: List[Dict]):
        """生成处理报告"""
        report = {
            "total_records": len(data),
            "by_source": {},
            "by_age_range": {},
            "by_content_type": {},
            "content_length_stats": {
                "min": 0,
                "max": 0,
                "avg": 0
            }
        }
        
        content_lengths = []
        
        for item in data:
            # 按来源统计
            source = item.get('source', 'unknown')
            report['by_source'][source] = report['by_source'].get(source, 0) + 1
            
            # 按年龄段统计
            age_range = item.get('age_range', 'unknown')
            report['by_age_range'][age_range] = report['by_age_range'].get(age_range, 0) + 1
            
            # 按内容类型统计
            content_type = item.get('content_type', 'unknown')
            report['by_content_type'][content_type] = report['by_content_type'].get(content_type, 0) + 1
            
            # 内容长度统计
            content_length = len(item.get('content', ''))
            content_lengths.append(content_length)
        
        if content_lengths:
            report['content_length_stats'] = {
                "min": min(content_lengths),
                "max": max(content_lengths),
                "avg": sum(content_lengths) / len(content_lengths)
            }
        
        with open("data/processed/processing_report.json", 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info("数据处理报告已生成")

def main():
    """主函数"""
    preprocessor = DataPreprocessor()
    processed_data = preprocessor.process_all_data()
    preprocessor.save_processed_data(processed_data)

if __name__ == "__main__":
    main()
