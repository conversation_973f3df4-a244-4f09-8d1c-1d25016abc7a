#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
婴幼儿语言发展指导数据集使用示例
"""

import json
import pandas as pd
from pathlib import Path

def load_dataset(file_path="data/final/infant_language_development_dataset.json"):
    """加载数据集"""
    if not Path(file_path).exists():
        print(f"数据集文件不存在: {file_path}")
        return None
    
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    return pd.DataFrame(data)

def explore_dataset(df):
    """探索数据集基本信息"""
    print("=== 数据集基本信息 ===")
    print(f"总记录数: {len(df)}")
    print(f"字段数: {len(df.columns)}")
    print(f"字段名: {list(df.columns)}")
    
    print("\n=== 数据分布 ===")
    print("按数据来源分布:")
    print(df['source'].value_counts())
    
    print("\n按年龄段分布:")
    print(df['age_range'].value_counts())
    
    print("\n按内容类型分布:")
    print(df['content_type'].value_counts())

def search_by_age(df, age_range):
    """按年龄段搜索数据"""
    filtered_data = df[df['age_range'] == age_range]
    print(f"\n=== {age_range} 年龄段数据 ===")
    print(f"共找到 {len(filtered_data)} 条数据")
    
    for idx, row in filtered_data.iterrows():
        print(f"\n标题: {row['title']}")
        print(f"来源: {row['source']}")
        print(f"内容类型: {row['content_type']}")
        print(f"内容摘要: {row['content'][:100]}...")

def search_by_keyword(df, keyword):
    """按关键词搜索数据"""
    # 在标题和内容中搜索关键词
    mask = df['title'].str.contains(keyword, na=False) | \
           df['content'].str.contains(keyword, na=False)
    
    filtered_data = df[mask]
    print(f"\n=== 包含关键词 '{keyword}' 的数据 ===")
    print(f"共找到 {len(filtered_data)} 条数据")
    
    for idx, row in filtered_data.iterrows():
        print(f"\n标题: {row['title']}")
        print(f"年龄段: {row['age_range']}")
        print(f"内容摘要: {row['content'][:100]}...")

def get_qa_pairs(df):
    """获取问答对数据"""
    qa_data = df[df['content_type'] == 'qa_pair']
    print(f"\n=== 问答对数据 ===")
    print(f"共有 {len(qa_data)} 个问答对")
    
    for idx, row in qa_data.head(3).iterrows():  # 只显示前3个
        print(f"\n问题: {row['title']}")
        print(f"年龄段: {row['age_range']}")
        print(f"答案: {row['content'][:150]}...")

def get_government_guidelines(df):
    """获取政府指导文件"""
    gov_data = df[df['source'] == 'government']
    print(f"\n=== 政府指导文件 ===")
    print(f"共有 {len(gov_data)} 份文件")
    
    for idx, row in gov_data.iterrows():
        metadata = row.get('metadata', {})
        print(f"\n标题: {row['title']}")
        print(f"发布机构: {metadata.get('original_source', '未知')}")
        print(f"发布日期: {metadata.get('publish_date', '未知')}")
        print(f"内容摘要: {row['content'][:100]}...")

def export_by_age_range(df, age_range, output_file):
    """按年龄段导出数据"""
    filtered_data = df[df['age_range'] == age_range]
    
    # 导出为JSON格式
    output_data = filtered_data.to_dict('records')
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n{age_range} 年龄段的 {len(filtered_data)} 条数据已导出到 {output_file}")

def main():
    """主函数 - 演示数据集的各种使用方法"""
    print("婴幼儿语言发展指导数据集使用示例")
    print("=" * 50)
    
    # 1. 加载数据集
    df = load_dataset()
    if df is None:
        return
    
    # 2. 探索数据集
    explore_dataset(df)
    
    # 3. 按年龄段搜索
    search_by_age(df, "0-6months")
    
    # 4. 按关键词搜索
    search_by_keyword(df, "语言发展")
    
    # 5. 获取问答对
    get_qa_pairs(df)
    
    # 6. 获取政府指导文件
    get_government_guidelines(df)
    
    # 7. 按年龄段导出数据
    export_by_age_range(df, "1-2years", "1-2years_data.json")
    
    print("\n=== 使用建议 ===")
    print("1. 可以根据年龄段筛选相关数据")
    print("2. 可以按关键词搜索特定内容")
    print("3. 问答对数据适合用于对话系统训练")
    print("4. 政府文档提供权威的指导信息")
    print("5. 文献数据提供理论基础和专业知识")
    
    print("\n=== 数据集特点 ===")
    print("- 覆盖0-6岁各个年龄段")
    print("- 包含理论知识、实践指导和问答对话")
    print("- 数据来源权威，内容专业可靠")
    print("- 格式标准化，便于机器学习使用")

if __name__ == "__main__":
    main()
