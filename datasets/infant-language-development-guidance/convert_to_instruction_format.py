#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集格式转换工具
将婴幼儿语言发展数据集转换为instruction-input-output格式，适用于大模型微调训练
"""

import json
import os
from typing import Dict, List, Any

class DatasetConverter:
    def __init__(self, input_file: str, output_file: str):
        self.input_file = input_file
        self.output_file = output_file
        
    def load_original_dataset(self) -> List[Dict[str, Any]]:
        """加载原始数据集"""
        with open(self.input_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def convert_qa_pair(self, record: Dict[str, Any]) -> Dict[str, str]:
        """转换问答对话格式"""
        instruction = "你是一位专业的婴幼儿语言发展指导专家。请根据家长的问题，提供专业、实用、易懂的指导建议。"
        
        # 提取年龄信息作为上下文
        age_context = ""
        if record.get("age_range"):
            age_map = {
                "0-6months": "0-6个月",
                "6-12months": "6-12个月", 
                "1-2years": "1-2岁",
                "2-3years": "2-3岁",
                "0-3years": "0-3岁"
            }
            age_context = f"适用年龄段：{age_map.get(record['age_range'], record['age_range'])}\n"
        
        return {
            "instruction": instruction,
            "input": age_context + record["title"],
            "output": record["content"]
        }
    
    def convert_knowledge(self, record: Dict[str, Any]) -> Dict[str, str]:
        """转换理论知识格式"""
        instruction = "你是一位婴幼儿语言发展领域的专家学者。请详细解释相关的理论知识和发展规律。"
        
        return {
            "instruction": instruction,
            "input": f"请详细介绍：{record['title']}",
            "output": record["content"]
        }
    
    def convert_guidance_methods(self, record: Dict[str, Any]) -> Dict[str, str]:
        """转换指导方法格式"""
        instruction = "你是一位经验丰富的早教指导师。请详细介绍婴幼儿语言发展的具体指导方法和实施技巧。"
        
        return {
            "instruction": instruction,
            "input": f"请介绍{record['title']}的具体实施方法和注意事项",
            "output": record["content"]
        }
    
    def convert_general(self, record: Dict[str, Any]) -> Dict[str, str]:
        """转换一般内容格式"""
        instruction = "你是一位婴幼儿发展评估和政策解读专家。请提供准确、权威的信息和指导。"
        
        return {
            "instruction": instruction,
            "input": f"请详细说明：{record['title']}",
            "output": record["content"]
        }
    
    def convert_record(self, record: Dict[str, Any]) -> Dict[str, str]:
        """根据内容类型转换单条记录"""
        content_type = record.get("content_type", "qa_pair")
        
        if content_type == "qa_pair":
            return self.convert_qa_pair(record)
        elif content_type == "knowledge":
            return self.convert_knowledge(record)
        elif content_type == "guidance_methods":
            return self.convert_guidance_methods(record)
        elif content_type == "general":
            return self.convert_general(record)
        else:
            # 默认按问答对话处理
            return self.convert_qa_pair(record)
    
    def convert_dataset(self) -> List[Dict[str, str]]:
        """转换整个数据集"""
        original_data = self.load_original_dataset()
        converted_data = []
        
        print(f"开始转换数据集，共{len(original_data)}条记录...")
        
        for i, record in enumerate(original_data):
            try:
                converted_record = self.convert_record(record)
                converted_data.append(converted_record)
                
                if (i + 1) % 100 == 0:
                    print(f"已转换 {i + 1}/{len(original_data)} 条记录")
                    
            except Exception as e:
                print(f"转换第{i+1}条记录时出错: {e}")
                continue
        
        print(f"转换完成！成功转换{len(converted_data)}条记录")
        return converted_data
    
    def save_converted_dataset(self, converted_data: List[Dict[str, str]]):
        """保存转换后的数据集"""
        with open(self.output_file, 'w', encoding='utf-8') as f:
            json.dump(converted_data, f, ensure_ascii=False, indent=2)
        
        print(f"转换后的数据集已保存到: {self.output_file}")
    
    def generate_statistics(self, converted_data: List[Dict[str, str]]) -> Dict[str, Any]:
        """生成转换统计信息"""
        stats = {
            "total_records": len(converted_data),
            "avg_input_length": sum(len(record["input"]) for record in converted_data) / len(converted_data),
            "avg_output_length": sum(len(record["output"]) for record in converted_data) / len(converted_data),
            "max_input_length": max(len(record["input"]) for record in converted_data),
            "max_output_length": max(len(record["output"]) for record in converted_data),
            "min_input_length": min(len(record["input"]) for record in converted_data),
            "min_output_length": min(len(record["output"]) for record in converted_data)
        }
        
        return stats

def main():
    # 文件路径配置
    input_file = "super_final_infant_language_development_dataset.json"
    output_file = "infant_language_development_instruction_dataset.json"
    stats_file = "conversion_statistics.json"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误：输入文件 {input_file} 不存在！")
        return
    
    # 创建转换器并执行转换
    converter = DatasetConverter(input_file, output_file)
    
    # 转换数据集
    converted_data = converter.convert_dataset()
    
    # 保存转换后的数据集
    converter.save_converted_dataset(converted_data)
    
    # 生成并保存统计信息
    stats = converter.generate_statistics(converted_data)
    with open(stats_file, 'w', encoding='utf-8') as f:
        json.dump(stats, f, ensure_ascii=False, indent=2)
    
    print(f"\n=== 转换统计信息 ===")
    print(f"总记录数: {stats['total_records']}")
    print(f"平均输入长度: {stats['avg_input_length']:.1f} 字符")
    print(f"平均输出长度: {stats['avg_output_length']:.1f} 字符")
    print(f"最大输入长度: {stats['max_input_length']} 字符")
    print(f"最大输出长度: {stats['max_output_length']} 字符")
    print(f"统计信息已保存到: {stats_file}")

if __name__ == "__main__":
    main()
