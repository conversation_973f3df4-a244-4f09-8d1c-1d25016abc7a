#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建简化版instruction格式数据集
专门用于大模型微调训练，格式更加标准化
"""

import json
import os
from typing import Dict, List, Any

class SimplifiedDatasetConverter:
    def __init__(self, input_file: str, output_file: str):
        self.input_file = input_file
        self.output_file = output_file
        
    def load_original_dataset(self) -> List[Dict[str, Any]]:
        """加载原始数据集"""
        with open(self.input_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def create_instruction_record(self, record: Dict[str, Any]) -> Dict[str, str]:
        """创建标准化的instruction格式记录"""
        content_type = record.get("content_type", "qa_pair")
        
        # 统一的instruction模板
        base_instruction = "你是一位专业的婴幼儿语言发展指导专家。"
        
        if content_type == "qa_pair":
            # 问答对话：直接使用家长问题作为input
            instruction = base_instruction + "请根据家长的问题，提供专业、实用、易懂的指导建议。"
            input_text = record["title"]
            output_text = record["content"]
            
        elif content_type == "knowledge":
            # 理论知识：转换为知识问答
            instruction = base_instruction + "请详细解释婴幼儿语言发展的相关理论知识。"
            input_text = f"请详细介绍{record['title']}的相关理论和观点。"
            output_text = record["content"]
            
        elif content_type == "guidance_methods":
            # 指导方法：转换为方法咨询
            instruction = base_instruction + "请详细介绍婴幼儿语言发展的具体指导方法。"
            input_text = f"请介绍{record['title']}的具体实施方法和注意事项。"
            output_text = record["content"]
            
        elif content_type == "general":
            # 一般内容：转换为政策解读
            instruction = base_instruction + "请提供准确、权威的婴幼儿发展信息和指导。"
            input_text = f"请详细说明{record['title']}的相关内容。"
            output_text = record["content"]
            
        else:
            # 默认处理
            instruction = base_instruction + "请根据问题提供专业指导。"
            input_text = record.get("title", "")
            output_text = record.get("content", "")
        
        return {
            "instruction": instruction,
            "input": input_text,
            "output": output_text
        }
    
    def convert_dataset(self) -> List[Dict[str, str]]:
        """转换整个数据集"""
        original_data = self.load_original_dataset()
        converted_data = []
        
        print(f"开始转换数据集，共{len(original_data)}条记录...")
        
        for i, record in enumerate(original_data):
            try:
                converted_record = self.create_instruction_record(record)
                
                # 数据质量检查
                if (len(converted_record["input"]) > 5 and 
                    len(converted_record["output"]) > 10):
                    converted_data.append(converted_record)
                else:
                    print(f"跳过质量不达标的记录 {i+1}")
                
                if (i + 1) % 100 == 0:
                    print(f"已处理 {i + 1}/{len(original_data)} 条记录")
                    
            except Exception as e:
                print(f"处理第{i+1}条记录时出错: {e}")
                continue
        
        print(f"转换完成！成功转换{len(converted_data)}条记录")
        return converted_data
    
    def save_dataset(self, data: List[Dict[str, str]]):
        """保存数据集"""
        with open(self.output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"数据集已保存到: {self.output_file}")
    
    def save_jsonl_format(self, data: List[Dict[str, str]], jsonl_file: str):
        """保存为JSONL格式（每行一个JSON对象）"""
        with open(jsonl_file, 'w', encoding='utf-8') as f:
            for record in data:
                f.write(json.dumps(record, ensure_ascii=False) + '\n')
        
        print(f"JSONL格式数据集已保存到: {jsonl_file}")
    
    def generate_sample_file(self, data: List[Dict[str, str]], sample_file: str, sample_size: int = 10):
        """生成样本文件用于检查"""
        sample_data = data[:sample_size]
        with open(sample_file, 'w', encoding='utf-8') as f:
            json.dump(sample_data, f, ensure_ascii=False, indent=2)
        
        print(f"样本文件已保存到: {sample_file}")
    
    def print_statistics(self, data: List[Dict[str, str]]):
        """打印统计信息"""
        if not data:
            print("数据集为空")
            return
        
        input_lengths = [len(record["input"]) for record in data]
        output_lengths = [len(record["output"]) for record in data]
        
        print(f"\n=== 数据集统计信息 ===")
        print(f"总记录数: {len(data)}")
        print(f"平均输入长度: {sum(input_lengths)/len(input_lengths):.1f} 字符")
        print(f"平均输出长度: {sum(output_lengths)/len(output_lengths):.1f} 字符")
        print(f"输入长度范围: {min(input_lengths)} - {max(input_lengths)} 字符")
        print(f"输出长度范围: {min(output_lengths)} - {max(output_lengths)} 字符")
        
        # 长度分布统计
        short_inputs = sum(1 for length in input_lengths if length < 50)
        medium_inputs = sum(1 for length in input_lengths if 50 <= length < 100)
        long_inputs = sum(1 for length in input_lengths if length >= 100)
        
        print(f"\n输入长度分布:")
        print(f"  短文本(<50字符): {short_inputs} 条 ({short_inputs/len(data)*100:.1f}%)")
        print(f"  中等文本(50-100字符): {medium_inputs} 条 ({medium_inputs/len(data)*100:.1f}%)")
        print(f"  长文本(>=100字符): {long_inputs} 条 ({long_inputs/len(data)*100:.1f}%)")

def main():
    # 文件路径配置
    input_file = "super_final_infant_language_development_dataset.json"
    output_file = "infant_language_instruction_dataset_v2.json"
    jsonl_file = "infant_language_instruction_dataset_v2.jsonl"
    sample_file = "instruction_dataset_sample.json"
    
    # 检查输入文件
    if not os.path.exists(input_file):
        print(f"错误：输入文件 {input_file} 不存在！")
        return
    
    # 创建转换器
    converter = SimplifiedDatasetConverter(input_file, output_file)
    
    # 转换数据集
    converted_data = converter.convert_dataset()
    
    if not converted_data:
        print("转换失败，没有生成有效数据")
        return
    
    # 保存数据集
    converter.save_dataset(converted_data)
    converter.save_jsonl_format(converted_data, jsonl_file)
    converter.generate_sample_file(converted_data, sample_file)
    
    # 打印统计信息
    converter.print_statistics(converted_data)
    
    print(f"\n=== 文件生成完成 ===")
    print(f"JSON格式: {output_file}")
    print(f"JSONL格式: {jsonl_file}")
    print(f"样本文件: {sample_file}")

if __name__ == "__main__":
    main()
