{"default": {"description": "婴幼儿语言发展指导数据集 - 专门针对0-3岁婴幼儿语言发展指导的高质量中文数据集", "citation": "@dataset{infant_language_development_2024,\n  title={婴幼儿语言发展指导数据集},\n  author={AI Assistant},\n  year={2024},\n  publisher={GitHub},\n  version={4.0},\n  description={基于专业教材和AI生成的婴幼儿语言发展指导数据集}\n}", "homepage": "https://github.com/your-repo/infant-language-development-guidance", "license": "CC BY 4.0", "features": {"id": {"dtype": "string", "_type": "Value", "description": "数据记录的唯一标识符"}, "source": {"dtype": "string", "_type": "Value", "description": "数据来源类型 (qa, literature, government)"}, "category": {"dtype": "string", "_type": "Value", "description": "数据类别，主要为language_development"}, "age_range": {"dtype": "string", "_type": "Value", "description": "适用年龄段 (0-3years, 0-6months, 1-2years, 2-3years)"}, "content_type": {"dtype": "string", "_type": "Value", "description": "内容类型 (qa_pair, knowledge, guidance_methods, general)"}, "title": {"dtype": "string", "_type": "Value", "description": "问题或标题内容"}, "content": {"dtype": "string", "_type": "Value", "description": "回答或正文内容"}, "metadata": {"_type": "dict", "description": "包含生成信息、参考页码、难度等级等元数据", "feature": {"generated_by": {"dtype": "string", "_type": "Value", "description": "生成方式 (qwen_api, manual, ocr_extraction)"}, "model": {"dtype": "string", "_type": "Value", "description": "使用的AI模型 (qwen-turbo-latest)"}, "batch": {"dtype": "string", "_type": "Value", "description": "生成批次 (first_100, second_400)"}, "original_content_type": {"dtype": "string", "_type": "Value", "description": "原始内容类型 (实践指导, 理论知识, 发展里程碑)"}, "reference_page": {"dtype": "int32", "_type": "Value", "description": "参考教材页码"}, "source_book": {"dtype": "string", "_type": "Value", "description": "来源书籍名称"}, "question_type": {"dtype": "string", "_type": "Value", "description": "问题类型 (practical_guidance, theoretical_knowledge)"}, "difficulty": {"dtype": "string", "_type": "Value", "description": "难度等级 (intermediate, advanced)"}, "conversation_format": {"dtype": "bool", "_type": "Value", "description": "是否为对话格式"}, "enhanced": {"dtype": "bool", "_type": "Value", "description": "是否为增强版本"}, "variant_type": {"dtype": "string", "_type": "Value", "description": "变体类型 (simplified_question, summary_answer)"}}}}, "supervised_keys": {"input": "title", "output": "content"}, "task_templates": [{"task": "conversational", "input_column": "title", "target_column": "content"}, {"task": "question-answering", "question_column": "title", "context_column": "content", "answers_column": "content"}], "splits": {"train": {"name": "train", "num_bytes": 2847392, "num_examples": 707, "dataset_name": "infant_language_development"}}, "download_checksums": {"super_final_infant_language_development_dataset.json": {"num_bytes": 2847392, "checksum": null}, "super_final_dialogue_training_dataset.jsonl": {"num_bytes": 2654321, "checksum": null}}, "download_size": 5501713, "dataset_size": 2847392, "size_in_bytes": 8349105, "config_name": "default", "version": {"version_str": "4.0.0", "major": 4, "minor": 0, "patch": 0}}, "dialogue_format": {"description": "专门用于对话模型训练的格式版本", "citation": "@dataset{infant_language_development_2024,\n  title={婴幼儿语言发展指导数据集-对话格式},\n  author={AI Assistant},\n  year={2024},\n  publisher={GitHub},\n  version={4.0}\n}", "homepage": "https://github.com/your-repo/infant-language-development-guidance", "license": "CC BY 4.0", "features": {"id": {"dtype": "string", "_type": "Value", "description": "对话记录的唯一标识符"}, "conversations": {"_type": "list", "description": "对话内容列表", "feature": {"_type": "dict", "feature": {"from": {"dtype": "string", "_type": "Value", "description": "发言者角色 (human, gpt)"}, "value": {"dtype": "string", "_type": "Value", "description": "发言内容"}}}}, "source": {"dtype": "string", "_type": "Value", "description": "数据来源"}, "age_range": {"dtype": "string", "_type": "Value", "description": "适用年龄段"}, "metadata": {"_type": "dict", "description": "对话元数据信息"}}, "supervised_keys": {"input": "conversations", "output": "conversations"}, "task_templates": [{"task": "conversational", "input_column": "conversations", "target_column": "conversations"}], "splits": {"train": {"name": "train", "num_bytes": 2654321, "num_examples": 675, "dataset_name": "infant_language_development_dialogue"}}, "download_checksums": {"super_final_dialogue_training_dataset.jsonl": {"num_bytes": 2654321, "checksum": null}}, "download_size": 2654321, "dataset_size": 2654321, "size_in_bytes": 5308642, "config_name": "dialogue_format", "version": {"version_str": "4.0.0", "major": 4, "minor": 0, "patch": 0}}}