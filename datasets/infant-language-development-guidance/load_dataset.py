#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
婴幼儿语言发展指导数据集加载器
Infant Language Development Guidance Dataset Loader

使用示例:
    from load_dataset import InfantLanguageDataset
    
    # 加载主数据集
    dataset = InfantLanguageDataset()
    
    # 获取统计信息
    stats = dataset.get_statistics()
    print(stats)
    
    # 筛选特定年龄段
    age_0_3 = dataset.filter_by_age('0-3years')
    
    # 筛选Qwen生成的对话
    qwen_data = dataset.filter_by_source('qwen_api')
"""

import json
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional, Union
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class InfantLanguageDataset:
    """婴幼儿语言发展指导数据集加载器"""
    
    def __init__(self, data_dir: Optional[str] = None):
        """
        初始化数据集加载器
        
        Args:
            data_dir: 数据集目录路径，默认为当前目录
        """
        self.data_dir = Path(data_dir) if data_dir else Path(__file__).parent
        self.main_dataset = None
        self.dialogue_dataset = None
        self.report = None
        
        # 自动加载数据
        self._load_all_data()
    
    def _load_all_data(self):
        """加载所有数据文件"""
        try:
            # 加载主数据集
            main_file = self.data_dir / "super_final_infant_language_development_dataset.json"
            if main_file.exists():
                with open(main_file, 'r', encoding='utf-8') as f:
                    self.main_dataset = json.load(f)
                logger.info(f"加载主数据集: {len(self.main_dataset)} 条记录")
            
            # 加载对话数据集
            dialogue_file = self.data_dir / "super_final_dialogue_training_dataset.jsonl"
            if dialogue_file.exists():
                self.dialogue_dataset = []
                with open(dialogue_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.strip():
                            self.dialogue_dataset.append(json.loads(line))
                logger.info(f"加载对话数据集: {len(self.dialogue_dataset)} 条记录")
            
            # 加载统计报告
            report_file = self.data_dir / "super_final_dataset_report.json"
            if report_file.exists():
                with open(report_file, 'r', encoding='utf-8') as f:
                    self.report = json.load(f)
                logger.info("加载统计报告完成")
                
        except Exception as e:
            logger.error(f"数据加载失败: {e}")
            raise
    
    def get_main_dataset(self) -> List[Dict]:
        """获取主数据集"""
        if self.main_dataset is None:
            raise ValueError("主数据集未加载")
        return self.main_dataset
    
    def get_dialogue_dataset(self) -> List[Dict]:
        """获取对话数据集"""
        if self.dialogue_dataset is None:
            raise ValueError("对话数据集未加载")
        return self.dialogue_dataset
    
    def get_statistics(self) -> Dict:
        """获取数据集统计信息"""
        if self.report:
            return self.report
        
        # 如果没有报告文件，动态计算统计信息
        if not self.main_dataset:
            return {}
        
        df = pd.DataFrame(self.main_dataset)
        
        stats = {
            'total_records': len(self.main_dataset),
            'age_distribution': df['age_range'].value_counts().to_dict(),
            'content_type_distribution': df['content_type'].value_counts().to_dict(),
            'source_distribution': df['source'].value_counts().to_dict(),
            'avg_content_length': df['content'].str.len().mean(),
            'avg_title_length': df['title'].str.len().mean()
        }
        
        # 统计Qwen生成的数据
        qwen_count = sum(1 for item in self.main_dataset 
                        if item.get('metadata', {}).get('generated_by') == 'qwen_api')
        stats['qwen_generated_count'] = qwen_count
        stats['qwen_percentage'] = (qwen_count / len(self.main_dataset)) * 100
        
        return stats
    
    def filter_by_age(self, age_range: str) -> List[Dict]:
        """
        按年龄段筛选数据
        
        Args:
            age_range: 年龄段 ('0-3years', '0-6months', '1-2years', '2-3years')
        
        Returns:
            筛选后的数据列表
        """
        if not self.main_dataset:
            return []
        
        return [item for item in self.main_dataset if item.get('age_range') == age_range]
    
    def filter_by_content_type(self, content_type: str) -> List[Dict]:
        """
        按内容类型筛选数据
        
        Args:
            content_type: 内容类型 ('qa_pair', 'knowledge', 'guidance_methods', 'general')
        
        Returns:
            筛选后的数据列表
        """
        if not self.main_dataset:
            return []
        
        return [item for item in self.main_dataset if item.get('content_type') == content_type]
    
    def filter_by_source(self, source_type: str) -> List[Dict]:
        """
        按数据来源筛选
        
        Args:
            source_type: 来源类型 ('qwen_api', 'manual', 'ocr_extraction')
        
        Returns:
            筛选后的数据列表
        """
        if not self.main_dataset:
            return []
        
        return [item for item in self.main_dataset 
                if item.get('metadata', {}).get('generated_by') == source_type]
    
    def filter_by_difficulty(self, difficulty: str) -> List[Dict]:
        """
        按难度等级筛选
        
        Args:
            difficulty: 难度等级 ('intermediate', 'advanced')
        
        Returns:
            筛选后的数据列表
        """
        if not self.main_dataset:
            return []
        
        return [item for item in self.main_dataset 
                if item.get('metadata', {}).get('difficulty') == difficulty]
    
    def search_content(self, keyword: str, field: str = 'all') -> List[Dict]:
        """
        搜索包含关键词的数据
        
        Args:
            keyword: 搜索关键词
            field: 搜索字段 ('title', 'content', 'all')
        
        Returns:
            包含关键词的数据列表
        """
        if not self.main_dataset:
            return []
        
        results = []
        for item in self.main_dataset:
            if field == 'title' and keyword in item.get('title', ''):
                results.append(item)
            elif field == 'content' and keyword in item.get('content', ''):
                results.append(item)
            elif field == 'all' and (keyword in item.get('title', '') or 
                                   keyword in item.get('content', '')):
                results.append(item)
        
        return results
    
    def get_random_sample(self, n: int = 5) -> List[Dict]:
        """
        获取随机样本
        
        Args:
            n: 样本数量
        
        Returns:
            随机样本列表
        """
        if not self.main_dataset:
            return []
        
        import random
        return random.sample(self.main_dataset, min(n, len(self.main_dataset)))
    
    def export_to_csv(self, output_path: str, include_metadata: bool = False):
        """
        导出为CSV格式
        
        Args:
            output_path: 输出文件路径
            include_metadata: 是否包含元数据
        """
        if not self.main_dataset:
            raise ValueError("没有数据可导出")
        
        df = pd.DataFrame(self.main_dataset)
        
        if not include_metadata:
            # 移除复杂的metadata列
            df = df.drop('metadata', axis=1, errors='ignore')
        
        df.to_csv(output_path, index=False, encoding='utf-8-sig')
        logger.info(f"数据已导出到: {output_path}")
    
    def export_filtered_data(self, filters: Dict, output_path: str):
        """
        导出筛选后的数据
        
        Args:
            filters: 筛选条件字典
            output_path: 输出文件路径
        """
        filtered_data = self.main_dataset.copy()
        
        # 应用筛选条件
        if 'age_range' in filters:
            filtered_data = [item for item in filtered_data 
                           if item.get('age_range') == filters['age_range']]
        
        if 'content_type' in filters:
            filtered_data = [item for item in filtered_data 
                           if item.get('content_type') == filters['content_type']]
        
        if 'generated_by' in filters:
            filtered_data = [item for item in filtered_data 
                           if item.get('metadata', {}).get('generated_by') == filters['generated_by']]
        
        # 保存筛选后的数据
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(filtered_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"筛选后的数据已保存到: {output_path} ({len(filtered_data)} 条记录)")
    
    def print_sample_data(self, n: int = 3):
        """打印样本数据"""
        if not self.main_dataset:
            print("没有数据可显示")
            return
        
        samples = self.get_random_sample(n)
        
        for i, item in enumerate(samples, 1):
            print(f"\n=== 样本 {i} ===")
            print(f"ID: {item.get('id', 'N/A')}")
            print(f"年龄段: {item.get('age_range', 'N/A')}")
            print(f"内容类型: {item.get('content_type', 'N/A')}")
            print(f"问题: {item.get('title', 'N/A')[:100]}...")
            print(f"回答: {item.get('content', 'N/A')[:200]}...")
            
            metadata = item.get('metadata', {})
            if metadata.get('generated_by'):
                print(f"生成方式: {metadata.get('generated_by')}")
            if metadata.get('batch'):
                print(f"生成批次: {metadata.get('batch')}")


def main():
    """示例用法"""
    # 加载数据集
    dataset = InfantLanguageDataset()
    
    # 显示统计信息
    print("=== 数据集统计信息 ===")
    stats = dataset.get_statistics()
    for key, value in stats.items():
        print(f"{key}: {value}")
    
    # 显示样本数据
    print("\n=== 样本数据 ===")
    dataset.print_sample_data(2)
    
    # 筛选示例
    print(f"\n=== 筛选示例 ===")
    qwen_data = dataset.filter_by_source('qwen_api')
    print(f"Qwen生成的数据: {len(qwen_data)} 条")
    
    age_0_3_data = dataset.filter_by_age('0-3years')
    print(f"0-3岁数据: {len(age_0_3_data)} 条")
    
    # 搜索示例
    search_results = dataset.search_content('语言发展')
    print(f"包含'语言发展'的数据: {len(search_results)} 条")


if __name__ == "__main__":
    main()
